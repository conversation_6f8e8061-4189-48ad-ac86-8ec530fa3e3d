{"name": "whatwg-url", "version": "13.0.0", "description": "An implementation of the WHATWG URL Standard's URL API and parsing machinery", "main": "index.js", "files": ["index.js", "webidl2js-wrapper.js", "lib/*.js"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "repository": "jsdom/whatwg-url", "dependencies": {"tr46": "^4.1.1", "webidl-conversions": "^7.0.0"}, "devDependencies": {"@domenic/eslint-config": "^3.0.0", "benchmark": "^2.1.4", "domexception": "^4.0.0", "esbuild": "^0.17.19", "eslint": "^8.41.0", "jest": "^29.5.0", "minipass-fetch": "^3.0.3", "webidl2js": "^17.1.0"}, "engines": {"node": ">=16"}, "scripts": {"coverage": "jest --coverage", "lint": "eslint .", "prepare": "node scripts/transform.js", "pretest": "node scripts/get-latest-platform-tests.js && node scripts/transform.js", "build-live-viewer": "esbuild --bundle --format=esm --sourcemap --outfile=live-viewer/whatwg-url.mjs index.js", "test": "jest"}, "jest": {"collectCoverageFrom": ["lib/**/*.js", "!lib/utils.js"], "coverageDirectory": "coverage", "coverageReporters": ["lcov", "text-summary"], "testEnvironment": "node", "testMatch": ["<rootDir>/test/**/*.js"], "testPathIgnorePatterns": ["^<rootDir>/test/testharness.js$", "^<rootDir>/test/web-platform-tests/"]}}