"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateSearchIndexOperation = void 0;
const operation_1 = require("../operation");
/** @internal */
class UpdateSearchIndexOperation extends operation_1.AbstractOperation {
    constructor(collection, name, definition) {
        super();
        this.collection = collection;
        this.name = name;
        this.definition = definition;
    }
    async execute(server, session) {
        const namespace = this.collection.fullNamespace;
        const command = {
            updateSearchIndex: namespace.collection,
            name: this.name,
            definition: this.definition
        };
        await server.commandAsync(namespace, command, { session });
        return;
    }
}
exports.UpdateSearchIndexOperation = UpdateSearchIndexOperation;
//# sourceMappingURL=update.js.map