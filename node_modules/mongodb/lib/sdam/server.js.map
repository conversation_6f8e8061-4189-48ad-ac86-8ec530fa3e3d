{"version": 3, "file": "server.js", "sourceRoot": "", "sources": ["../../src/sdam/server.ts"], "names": [], "mappings": ";;;AAAA,+BAAiC;AAIjC,mDAA0F;AAC1F,6DAIiC;AACjC,2CAAkD;AAClD,4CAWsB;AACtB,oCAekB;AAElB,gDAAmD;AAGnD,kDAAuD;AACvD,oCAOkB;AAClB,qCAOkB;AAMlB,uCAAyD;AACzD,6DAAiF;AAGjF,MAAM,eAAe,GAAG,IAAA,wBAAgB,EAAC;IACvC,CAAC,qBAAY,CAAC,EAAE,CAAC,qBAAY,EAAE,yBAAgB,CAAC;IAChD,CAAC,yBAAgB,CAAC,EAAE,CAAC,yBAAgB,EAAE,sBAAa,EAAE,wBAAe,EAAE,qBAAY,CAAC;IACpF,CAAC,wBAAe,CAAC,EAAE,CAAC,wBAAe,EAAE,sBAAa,EAAE,qBAAY,CAAC;IACjE,CAAC,sBAAa,CAAC,EAAE,CAAC,sBAAa,EAAE,qBAAY,CAAC;CAC/C,CAAC,CAAC;AAiCH,gBAAgB;AAChB,MAAa,MAAO,SAAQ,+BAA+B;IA2BzD;;OAEG;IACH,YAAY,QAAkB,EAAE,WAA8B,EAAE,OAAsB;QACpF,KAAK,EAAE,CAAC;QAER,IAAI,CAAC,YAAY,GAAG,IAAA,gBAAS,EAC3B,CACE,EAAoB,EACpB,GAAa,EACb,OAAuB;QACvB,oHAAoH;QACpH,QAA8D,EAC9D,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,QAAe,CAAC,CACrD,CAAC;QAEF,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;QAEnC,MAAM,WAAW,GAAG,EAAE,WAAW,EAAE,WAAW,CAAC,WAAW,EAAE,GAAG,OAAO,EAAE,CAAC;QAEzE,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,IAAI,GAAG,IAAI,gCAAc,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;QAElD,IAAI,CAAC,CAAC,GAAG;YACP,WAAW;YACX,OAAO;YACP,KAAK,EAAE,qBAAY;YACnB,cAAc,EAAE,CAAC;SAClB,CAAC;QAEF,KAAK,MAAM,KAAK,IAAI,CAAC,GAAG,uBAAW,EAAE,GAAG,sBAAU,CAAC,EAAE;YACnD,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,CAAM,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC;SACtD;QAED,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,uBAAU,CAAC,qBAAqB,EAAE,CAAC,WAAwB,EAAE,EAAE;YAC1E,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QACjC,CAAC,CAAC,CAAC;QAEH,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;YACpB,gDAAgD;YAChD,OAAO;SACR;QAED,qBAAqB;QACrB,IAAI,CAAC,OAAO,GAAG,IAAI,iBAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;QAEjD,KAAK,MAAM,KAAK,IAAI,4BAAgB,EAAE;YACpC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,CAAM,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC;SACzD;QAED,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC,KAAiB,EAAE,EAAE,CAAC,iBAAiB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;QACtF,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,MAAM,CAAC,0BAA0B,EAAE,CAAC,KAAoC,EAAE,EAAE;YAC1F,IAAI,CAAC,IAAI,CACP,MAAM,CAAC,oBAAoB,EAC3B,IAAI,sCAAiB,CAAC,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,KAAK,CAAC,KAAK,EAAE;gBAC/D,aAAa,EAAE,sBAAsB,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,KAAK,CAAC,QAAQ,CAAC;aACtF,CAAC,CACH,CAAC;YAEF,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,KAAK,yBAAgB,EAAE;gBACrC,eAAe,CAAC,IAAI,EAAE,wBAAe,CAAC,CAAC;gBACvC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;aACjC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;IACnC,CAAC;IAED,IAAI,WAAW,CAAC,WAAoC;QAClD,IAAI,CAAC,QAAQ,CAAC,WAAW,GAAG,WAAW,CAAC;IAC1C,CAAC;IAED,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,CAAC,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,CAAC,CAAC,WAAW,CAAC,OAAO,CAAC;IACpC,CAAC;IAED,IAAI,aAAa;QACf,IAAI,IAAI,CAAC,CAAC,CAAC,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,aAAa,EAAE;YAClD,OAAO,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC;SACrC;QACD,OAAO;IACT,CAAC;IAED,IAAI,YAAY;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,KAAK,qBAAY,CAAC,YAAY,CAAC;IACtE,CAAC;IAED;;OAEG;IACH,OAAO;QACL,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,KAAK,qBAAY,EAAE;YACjC,OAAO;SACR;QAED,eAAe,CAAC,IAAI,EAAE,yBAAgB,CAAC,CAAC;QAExC,8DAA8D;QAC9D,8DAA8D;QAC9D,kBAAkB;QAClB,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACtB,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,CAAC;SACzB;aAAM;YACL,eAAe,CAAC,IAAI,EAAE,wBAAe,CAAC,CAAC;YACvC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;SACjC;IACH,CAAC;IAED,oCAAoC;IACpC,OAAO,CAAC,OAAwB,EAAE,QAAmB;QACnD,IAAI,OAAO,OAAO,KAAK,UAAU,EAAE;YACjC,QAAQ,GAAG,OAAO,CAAC;YACnB,OAAO,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC;SAC5B;QACD,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,OAAO,CAAC,CAAC;QAEvD,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,KAAK,qBAAY,EAAE;YACjC,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE;gBAClC,QAAQ,EAAE,CAAC;aACZ;YAED,OAAO;SACR;QAED,eAAe,CAAC,IAAI,EAAE,sBAAa,CAAC,CAAC;QAErC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACtB,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,CAAC;SACvB;QAED,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,GAAG,CAAC,EAAE;YAC7B,eAAe,CAAC,IAAI,EAAE,qBAAY,CAAC,CAAC;YACpC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACpB,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE;gBAClC,QAAQ,CAAC,GAAG,CAAC,CAAC;aACf;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;OAGG;IACH,YAAY;QACV,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACtB,IAAI,CAAC,OAAO,EAAE,YAAY,EAAE,CAAC;SAC9B;IACH,CAAC;IAED;;;OAGG;IACH,OAAO,CACL,EAAoB,EACpB,GAAa,EACb,OAAuB,EACvB,QAA4B;QAE5B,IAAI,QAAQ,IAAI,IAAI,EAAE;YACpB,MAAM,IAAI,iCAAyB,CAAC,2BAA2B,CAAC,CAAC;SAClE;QAED,IAAI,EAAE,CAAC,EAAE,IAAI,IAAI,IAAI,OAAO,EAAE,KAAK,QAAQ,EAAE;YAC3C,MAAM,IAAI,iCAAyB,CAAC,gCAAgC,CAAC,CAAC;SACvE;QAED,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,KAAK,sBAAa,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,KAAK,qBAAY,EAAE;YACnE,QAAQ,CAAC,IAAI,8BAAsB,EAAE,CAAC,CAAC;YACvC,OAAO;SACR;QAED,oBAAoB;QACpB,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,EAAE,EAAE,mBAAmB,EAAE,KAAK,EAAE,CAAC,CAAC;QAEhF,+EAA+E;QAC/E,gFAAgF;QAChF,iFAAiF;QACjF,4EAA4E;QAC5E,IAAI,YAAY,CAAC,kBAAkB,EAAE;YACnC,OAAO,YAAY,CAAC,cAAc,CAAC;SACpC;QAED,MAAM,OAAO,GAAG,YAAY,CAAC,OAAO,CAAC;QACrC,MAAM,IAAI,GAAG,OAAO,EAAE,gBAAgB,CAAC;QAEvC,0FAA0F;QAC1F,2FAA2F;QAC3F,oEAAoE;QACpE,QAAQ;QACR,yFAAyF;QACzF,4FAA4F;QAC5F,yFAAyF;QACzF,yFAAyF;QACzF,4FAA4F;QAC5F,yFAAyF;QACzF,2FAA2F;QAC3F,eAAe;QACf,IAAI,IAAI,CAAC,YAAY,IAAI,OAAO,IAAI,IAAI,IAAI,IAAI,IAAI,iBAAiB,CAAC,GAAG,EAAE,OAAO,CAAC,EAAE;YACnF,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE,UAAU,EAAE,EAAE;gBACrC,IAAI,GAAG,IAAI,UAAU,IAAI,IAAI,EAAE;oBAC7B,IAAI,QAAQ;wBAAE,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC;oBACnC,OAAO;iBACR;gBAED,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;gBACxB,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE,GAAG,EAAE,YAAY,EAAE,QAAQ,CAAC,CAAC;YAChD,CAAC,CAAC,CAAC;YACH,OAAO;SACR;QAED,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAE/B,IAAI,CAAC,IAAI,CAAC,cAAc,CACtB,IAAI,EACJ,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE;YAChB,IAAI,GAAG,IAAI,CAAC,IAAI,EAAE;gBAChB,IAAI,CAAC,uBAAuB,EAAE,CAAC;gBAC/B,IAAI,CAAC,GAAG,EAAE;oBACR,OAAO,EAAE,CAAC,IAAI,yBAAiB,CAAC,2CAA2C,CAAC,CAAC,CAAC;iBAC/E;gBACD,IAAI,CAAC,CAAC,GAAG,YAAY,yBAAgB,CAAC,EAAE;oBACtC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;iBACvB;gBACD,OAAO,EAAE,CAAC,GAAG,CAAC,CAAC;aAChB;YAED,IAAI,CAAC,OAAO,CACV,EAAE,EACF,GAAG,EACH,YAAY,EACZ,oBAAoB,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,YAAY,EAAE,CAAC,KAAK,EAAE,QAAQ,EAAE,EAAE;gBACtE,IAAI,CAAC,uBAAuB,EAAE,CAAC;gBAC/B,EAAE,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;YACtB,CAAC,CAAC,CACH,CAAC;QACJ,CAAC,EACD,QAAQ,CACT,CAAC;IACJ,CAAC;IAED;;;OAGG;IACH,WAAW,CAAC,KAAe,EAAE,UAAuB;QAClD,IAAI,CAAC,CAAC,KAAK,YAAY,kBAAU,CAAC,EAAE;YAClC,OAAO;SACR;QAED,MAAM,YAAY,GAChB,KAAK,CAAC,oBAAoB,IAAI,KAAK,CAAC,oBAAoB,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC;QAClF,IAAI,YAAY,EAAE;YAChB,OAAO;SACR;QAED,MAAM,wBAAwB,GAC5B,KAAK,YAAY,yBAAiB,IAAI,CAAC,CAAC,KAAK,YAAY,gCAAwB,CAAC,CAAC;QACrF,MAAM,oCAAoC,GAAG,IAAA,qCAA6B,EAAC,KAAK,CAAC,CAAC;QAClF,MAAM,oBAAoB,GAAG,KAAK,CAAC,aAAa,CAAC,uBAAe,CAAC,cAAc,CAAC,CAAC;QACjF,IAAI,wBAAwB,IAAI,oCAAoC,IAAI,oBAAoB,EAAE;YAC5F,uEAAuE;YACvE,qCAAqC;YACrC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;gBACtB,KAAK,CAAC,aAAa,CAAC,uBAAe,CAAC,SAAS,CAAC,CAAC;gBAC/C,iBAAiB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;aAChC;iBAAM,IAAI,UAAU,EAAE;gBACrB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,UAAU,CAAC,SAAS,EAAE,CAAC,CAAC;aACtD;SACF;aAAM;YACL,IAAI,IAAA,gCAAwB,EAAC,KAAK,CAAC,EAAE;gBACnC,IAAI,4BAA4B,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE;oBAC7C,MAAM,eAAe,GAAG,IAAA,sBAAc,EAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAA,+BAAuB,EAAC,KAAK,CAAC,CAAC;oBACpF,IAAI,IAAI,CAAC,YAAY,IAAI,UAAU,IAAI,eAAe,EAAE;wBACtD,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,UAAU,CAAC,SAAS,EAAE,CAAC,CAAC;qBACtD;oBAED,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;wBACtB,IAAI,eAAe,EAAE;4BACnB,KAAK,CAAC,aAAa,CAAC,uBAAe,CAAC,SAAS,CAAC,CAAC;yBAChD;wBACD,iBAAiB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;wBAC/B,OAAO,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC;qBAC7C;iBACF;aACF;SACF;IACH,CAAC;IAED;;OAEG;IACK,uBAAuB;QAC7B,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,cAAc,IAAI,CAAC,CAAC,CAAC;IACtC,CAAC;IAED;;OAEG;IACK,uBAAuB;QAC7B,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,cAAc,IAAI,CAAC,CAAC,CAAC;IACtC,CAAC;;AAnUD,aAAa;AACG,+BAAwB,GAAG,oCAAwB,CAAC;AACpE,aAAa;AACG,iCAA0B,GAAG,sCAA0B,CAAC;AACxE,aAAa;AACG,8BAAuB,GAAG,mCAAuB,CAAC;AAClE,aAAa;AACG,cAAO,GAAG,mBAAO,CAAC;AAClC,aAAa;AACG,2BAAoB,GAAG,gCAAoB,CAAC;AAC5D,aAAa;AACG,aAAM,GAAG,kBAAM,CAAC;AAChC,aAAa;AACG,YAAK,GAAG,iBAAK,CAAC;AAzBnB,wBAAM;AAkVnB,SAAS,sBAAsB,CAAC,MAAc,EAAE,QAAgB;IAC9D,IAAI,MAAM,KAAK,CAAC,CAAC,EAAE;QACjB,OAAO,QAAQ,CAAC;KACjB;IAED,MAAM,KAAK,GAAG,GAAG,CAAC;IAClB,OAAO,KAAK,GAAG,QAAQ,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC;AACjD,CAAC;AAED,SAAS,iBAAiB,CAAC,MAAc,EAAE,KAAwB;IACjE,qDAAqD;IACrD,IAAI,MAAM,CAAC,YAAY,EAAE;QACvB,OAAO;KACR;IAED,IAAI,KAAK,YAAY,yBAAiB,IAAI,CAAC,CAAC,KAAK,YAAY,gCAAwB,CAAC,EAAE;QACtF,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,CAAC;KACzB;IAED,MAAM,CAAC,IAAI,CACT,MAAM,CAAC,oBAAoB,EAC3B,IAAI,sCAAiB,CAAC,MAAM,CAAC,WAAW,CAAC,WAAW,EAAE,SAAS,EAAE,EAAE,KAAK,EAAE,CAAC,CAC5E,CAAC;AACJ,CAAC;AAED,SAAS,iBAAiB,CAAC,GAAa,EAAE,OAAuB;IAC/D,IAAI,OAAO,EAAE;QACX,OAAO,CACL,OAAO,CAAC,aAAa,EAAE;YACvB,WAAW,IAAI,GAAG;YAClB,MAAM,IAAI,GAAG;YACb,SAAS,IAAI,GAAG;YAChB,iBAAiB,IAAI,GAAG;YACxB,aAAa,IAAI,GAAG,CACrB,CAAC;KACH;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,iBAAiB,CAAC,IAAoB,EAAE,UAAsB;IACrE,IAAI,UAAU,CAAC,SAAS,EAAE;QACxB,OAAO,CACL,UAAU,CAAC,UAAU,KAAK,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,UAAU,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,CAC1F,CAAC;KACH;IAED,OAAO,UAAU,CAAC,UAAU,KAAK,IAAI,CAAC,UAAU,CAAC;AACnD,CAAC;AAED,SAAS,4BAA4B,CAAC,MAAc,EAAE,GAAe;IACnE,MAAM,GAAG,GAAG,GAAG,CAAC,eAAe,CAAC;IAChC,MAAM,GAAG,GAAG,MAAM,CAAC,WAAW,CAAC,eAAe,CAAC;IAC/C,OAAO,IAAA,2CAAsB,EAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;AAC9C,CAAC;AAED,SAAS,mBAAmB,CAAC,OAAkC,EAAE,GAAa;IAC5E,OAAO,OAAO,IAAI,OAAO,CAAC,aAAa,EAAE,IAAI,CAAC,IAAA,mCAAoB,EAAC,GAAG,CAAC,CAAC;AAC1E,CAAC;AAED;4DAC4D;AAC5D,SAAS,wBAAwB,CAAC,QAAkB;IAClD,OAAO,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC;AAClD,CAAC;AAED,SAAS,oBAAoB,CAC3B,MAAc,EACd,UAAsB,EACtB,GAAa,EACb,OAAoD,EACpD,QAAkB;IAElB,MAAM,OAAO,GAAG,OAAO,EAAE,OAAO,CAAC;IACjC,OAAO,SAAS,qBAAqB,CAAC,KAAK,EAAE,MAAM;QACjD,mDAAmD;QACnD,IAAI,KAAK,IAAI,IAAI,IAAI,MAAM,IAAI,IAAI,EAAE;YACnC,OAAO,QAAQ,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;SACpC;QAED,IAAI,OAAO,IAAI,IAAI,IAAI,YAAY,IAAI,OAAO,IAAI,OAAO,CAAC,UAAU,KAAK,IAAI,EAAE;YAC7E,OAAO,QAAQ,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;SAClC;QAED,IAAI,CAAC,KAAK,EAAE;YACV,OAAO,QAAQ,CAAC,IAAI,0CAAkC,CAAC,8BAA8B,CAAC,CAAC,CAAC;SACzF;QAED,IAAI,CAAC,CAAC,KAAK,YAAY,kBAAU,CAAC,EAAE;YAClC,+DAA+D;YAC/D,OAAO,QAAQ,CAAC,KAAK,CAAC,CAAC;SACxB;QAED,IAAI,iBAAiB,CAAC,MAAM,CAAC,IAAI,EAAE,UAAU,CAAC,EAAE;YAC9C,OAAO,QAAQ,CAAC,KAAK,CAAC,CAAC;SACxB;QAED,IAAI,KAAK,YAAY,yBAAiB,EAAE;YACtC,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,aAAa,EAAE;gBACzD,OAAO,CAAC,aAAa,CAAC,OAAO,GAAG,IAAI,CAAC;aACtC;YAED,sDAAsD;YACtD,IACE,mBAAmB,CAAC,OAAO,EAAE,GAAG,CAAC;gBACjC,CAAC,KAAK,CAAC,aAAa,CAAC,uBAAe,CAAC,yBAAyB,CAAC,EAC/D;gBACA,KAAK,CAAC,aAAa,CAAC,uBAAe,CAAC,yBAAyB,CAAC,CAAC;aAChE;YAED,IACE,CAAC,wBAAwB,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,IAAA,mCAAoB,EAAC,GAAG,CAAC,CAAC;gBACxE,IAAA,+BAAuB,EAAC,MAAM,CAAC;gBAC/B,CAAC,mBAAmB,CAAC,OAAO,EAAE,GAAG,CAAC,EAClC;gBACA,KAAK,CAAC,aAAa,CAAC,uBAAe,CAAC,mBAAmB,CAAC,CAAC;aAC1D;SACF;aAAM;YACL,IACE,CAAC,wBAAwB,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,IAAA,mCAAoB,EAAC,GAAG,CAAC,CAAC;gBACxE,IAAA,gCAAwB,EAAC,KAAK,EAAE,IAAA,sBAAc,EAAC,MAAM,CAAC,CAAC;gBACvD,CAAC,mBAAmB,CAAC,OAAO,EAAE,GAAG,CAAC,EAClC;gBACA,KAAK,CAAC,aAAa,CAAC,uBAAe,CAAC,mBAAmB,CAAC,CAAC;aAC1D;SACF;QAED,IACE,OAAO;YACP,OAAO,CAAC,QAAQ;YAChB,KAAK,CAAC,aAAa,CAAC,uBAAe,CAAC,yBAAyB,CAAC,EAC9D;YACA,OAAO,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;SAChC;QAED,MAAM,CAAC,WAAW,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;QAEtC,OAAO,QAAQ,CAAC,KAAK,CAAC,CAAC;IACzB,CAAC,CAAC;AACJ,CAAC"}