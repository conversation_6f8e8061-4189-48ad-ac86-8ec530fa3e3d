{"version": 3, "file": "mongodb_aws.js", "sourceRoot": "", "sources": ["../../../src/cmap/auth/mongodb_aws.ts"], "names": [], "mappings": ";;;AAAA,iCAAiC;AACjC,mCAAmC;AACnC,+BAAiC;AAGjC,mCAAmC;AACnC,qCAA4D;AAC5D,uCAKqB;AACrB,uCAAqE;AACrE,mDAAiE;AACjE,2DAAuD;AACvD,2CAA4C;AAE5C;;;GAGG;AACH,MAAM,cAAc,GAAG,IAAI,GAAG,CAAC;IAC7B,gBAAgB;IAChB,YAAY;IACZ,gBAAgB;IAChB,gBAAgB;IAChB,YAAY;IACZ,cAAc;IACd,cAAc;IACd,YAAY;IACZ,WAAW;IACX,WAAW;IACX,WAAW;IACX,WAAW;IACX,WAAW;IACX,WAAW;IACX,WAAW;IACX,WAAW;CACZ,CAAC,CAAC;AACH,MAAM,OAAO,GAAG,GAAG,CAAC;AACpB,MAAM,gBAAgB,GAAG,sBAAsB,CAAC;AAChD,MAAM,WAAW,GAAG,wBAAwB,CAAC;AAC7C,MAAM,YAAY,GAAG,4CAA4C,CAAC;AAClE,MAAM,WAAW,GAAyB;IACxC,WAAW,EAAE,KAAK;IAClB,YAAY,EAAE,IAAI;IAClB,aAAa,EAAE,IAAI;IACnB,cAAc,EAAE,KAAK;IACrB,UAAU,EAAE,KAAK;CAClB,CAAC;AAQF,MAAa,UAAW,SAAQ,4BAAY;IAI1C;QACE,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,gBAAgB,GAAG,IAAA,gBAAS,EAAC,MAAM,CAAC,WAAW,CAAC,CAAC;IACxD,CAAC;IAEQ,KAAK,CAAC,IAAI,CAAC,WAAwB;QAC1C,MAAM,EAAE,UAAU,EAAE,GAAG,WAAW,CAAC;QACnC,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE;YAC5B,MAAM,IAAI,oCAA4B,CAAC,uCAAuC,CAAC,CAAC;SACjF;QAED,IAAI,cAAc,IAAI,WAAI,EAAE;YAC1B,MAAM,WAAI,CAAC,cAAc,CAAC,CAAC;SAC5B;QACD,MAAM,EAAE,IAAI,EAAE,GAAG,WAAI,CAAC;QAEtB,IAAI,IAAA,sBAAc,EAAC,UAAU,CAAC,GAAG,CAAC,EAAE;YAClC,MAAM,IAAI,+BAAuB,CAC/B,kEAAkE,CACnE,CAAC;SACH;QAED,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,QAAQ,EAAE;YACrC,WAAW,CAAC,WAAW,GAAG,MAAM,mBAAmB,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;SAC9E;QAED,MAAM,EAAE,WAAW,EAAE,GAAG,WAAW,CAAC;QAEpC,MAAM,WAAW,GAAG,WAAW,CAAC,QAAQ,CAAC;QACzC,MAAM,eAAe,GAAG,WAAW,CAAC,QAAQ,CAAC;QAC7C,MAAM,YAAY,GAAG,WAAW,CAAC,mBAAmB,CAAC,iBAAiB,CAAC;QAEvE,kGAAkG;QAClG,MAAM,cAAc,GAClB,WAAW,IAAI,eAAe,IAAI,YAAY;YAC5C,CAAC,CAAC,EAAE,WAAW,EAAE,eAAe,EAAE,YAAY,EAAE;YAChD,CAAC,CAAC,WAAW,IAAI,eAAe;gBAChC,CAAC,CAAC,EAAE,WAAW,EAAE,eAAe,EAAE;gBAClC,CAAC,CAAC,SAAS,CAAC;QAEhB,MAAM,EAAE,GAAG,WAAW,CAAC,MAAM,CAAC;QAC9B,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC;QAE9C,MAAM,SAAS,GAAG;YAChB,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE,aAAa;YACxB,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,OAAO,EAAE,EAAE,WAAW,CAAC;SAC/D,CAAC;QAEF,MAAM,iBAAiB,GAAG,MAAM,UAAU,CAAC,YAAY,CAAC,IAAA,UAAE,EAAC,GAAG,EAAE,OAAO,CAAC,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;QAEhG,MAAM,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC,OAAO,CAAC,MAAM,EAAE,WAAW,CAGpF,CAAC;QACF,MAAM,IAAI,GAAG,cAAc,CAAC,CAAC,CAAC;QAC9B,MAAM,WAAW,GAAG,cAAc,CAAC,CAAC,CAAC,MAAM,CAAC;QAC5C,IAAI,WAAW,CAAC,MAAM,KAAK,EAAE,EAAE;YAC7B,kBAAkB;YAClB,MAAM,IAAI,yBAAiB,CAAC,+BAA+B,WAAW,CAAC,MAAM,eAAe,CAAC,CAAC;SAC/F;QAED,IAAI,CAAC,iBAAS,CAAC,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC,EAAE,KAAK,CAAC,EAAE;YACvE,0FAA0F;YAC1F,oHAAoH;YAEpH,kBAAkB;YAClB,MAAM,IAAI,yBAAiB,CAAC,+CAA+C,CAAC,CAAC;SAC9E;QAED,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,MAAM,GAAG,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;YACrE,kBAAkB;YAClB,MAAM,IAAI,yBAAiB,CAAC,qCAAqC,IAAI,GAAG,CAAC,CAAC;SAC3E;QAED,MAAM,IAAI,GAAG,6CAA6C,CAAC;QAC3D,MAAM,OAAO,GAAG,IAAI,CAClB;YACE,MAAM,EAAE,MAAM;YACd,IAAI;YACJ,MAAM,EAAE,YAAY,CAAC,cAAc,CAAC,CAAC,CAAC;YACtC,OAAO,EAAE,KAAK;YACd,OAAO,EAAE;gBACP,cAAc,EAAE,mCAAmC;gBACnD,gBAAgB,EAAE,IAAI,CAAC,MAAM;gBAC7B,wBAAwB,EAAE,iBAAS,CAAC,QAAQ,CAAC,WAAW,CAAC;gBACzD,uBAAuB,EAAE,GAAG;aAC7B;YACD,IAAI,EAAE,GAAG;YACT,IAAI;SACL,EACD,cAAc,CACf,CAAC;QAEF,MAAM,OAAO,GAA2B;YACtC,CAAC,EAAE,OAAO,CAAC,OAAO,CAAC,aAAa;YAChC,CAAC,EAAE,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC;SACjC,CAAC;QAEF,IAAI,YAAY,EAAE;YAChB,OAAO,CAAC,CAAC,GAAG,YAAY,CAAC;SAC1B;QAED,MAAM,YAAY,GAAG;YACnB,YAAY,EAAE,CAAC;YACf,cAAc,EAAE,CAAC;YACjB,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,WAAW,CAAC;SAC9C,CAAC;QAEF,MAAM,UAAU,CAAC,YAAY,CAAC,IAAA,UAAE,EAAC,GAAG,EAAE,OAAO,CAAC,EAAE,YAAY,EAAE,SAAS,CAAC,CAAC;IAC3E,CAAC;;AAjHM,6BAAkB,GAAuD,IAAI,CAAC;AAD1E,gCAAU;AA6HvB,KAAK,UAAU,mBAAmB,CAAC,WAA6B;IAC9D,SAAS,+BAA+B,CAAC,KAAyB;QAChE,IAAI,CAAC,KAAK,CAAC,WAAW,IAAI,CAAC,KAAK,CAAC,eAAe,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE;YAChE,MAAM,IAAI,oCAA4B,CAAC,oDAAoD,CAAC,CAAC;SAC9F;QAED,OAAO,IAAI,oCAAgB,CAAC;YAC1B,QAAQ,EAAE,KAAK,CAAC,WAAW;YAC3B,QAAQ,EAAE,KAAK,CAAC,eAAe;YAC/B,MAAM,EAAE,WAAW,CAAC,MAAM;YAC1B,SAAS,EAAE,yBAAa,CAAC,WAAW;YACpC,mBAAmB,EAAE;gBACnB,iBAAiB,EAAE,KAAK,CAAC,KAAK;aAC/B;SACF,CAAC,CAAC;IACL,CAAC;IAED,UAAU,CAAC,kBAAkB,KAAK,IAAA,+BAAwB,GAAE,CAAC;IAE7D,wEAAwE;IACxE,sBAAsB;IACtB,IAAI,cAAc,IAAI,UAAU,CAAC,kBAAkB,EAAE;QACnD,qEAAqE;QACrE,sEAAsE;QACtE,IAAI,OAAO,CAAC,GAAG,CAAC,sCAAsC,EAAE;YACtD,OAAO,+BAA+B,CACpC,MAAM,IAAA,eAAO,EAAC,GAAG,gBAAgB,GAAG,OAAO,CAAC,GAAG,CAAC,sCAAsC,EAAE,CAAC,CAC1F,CAAC;SACH;QAED,6CAA6C;QAE7C,cAAc;QACd,MAAM,KAAK,GAAG,MAAM,IAAA,eAAO,EAAC,GAAG,WAAW,mBAAmB,EAAE;YAC7D,MAAM,EAAE,KAAK;YACb,IAAI,EAAE,KAAK;YACX,OAAO,EAAE,EAAE,sCAAsC,EAAE,EAAE,EAAE;SACxD,CAAC,CAAC;QAEH,gBAAgB;QAChB,MAAM,QAAQ,GAAG,MAAM,IAAA,eAAO,EAAC,GAAG,WAAW,IAAI,YAAY,EAAE,EAAE;YAC/D,IAAI,EAAE,KAAK;YACX,OAAO,EAAE,EAAE,0BAA0B,EAAE,KAAK,EAAE;SAC/C,CAAC,CAAC;QAEH,uBAAuB;QACvB,MAAM,KAAK,GAAG,MAAM,IAAA,eAAO,EAAC,GAAG,WAAW,IAAI,YAAY,IAAI,QAAQ,EAAE,EAAE;YACxE,OAAO,EAAE,EAAE,0BAA0B,EAAE,KAAK,EAAE;SAC/C,CAAC,CAAC;QAEH,OAAO,+BAA+B,CAAC,KAAK,CAAC,CAAC;KAC/C;SAAM;QACL,IAAI,EAAE,0BAA0B,GAAG,EAAE,EAAE,UAAU,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC,GAAG,CAAC;QACvE,0BAA0B,GAAG,0BAA0B,CAAC,WAAW,EAAE,CAAC;QACtE,UAAU,GAAG,UAAU,CAAC,WAAW,EAAE,CAAC;QAEtC,6IAA6I;QAC7I,MAAM,sBAAsB,GAC1B,UAAU,CAAC,MAAM,KAAK,CAAC,IAAI,0BAA0B,CAAC,MAAM,KAAK,CAAC,CAAC;QAErE;;;;;;WAMG;QACH,MAAM,cAAc,GAClB,0BAA0B,KAAK,UAAU;YACzC,CAAC,0BAA0B,KAAK,QAAQ,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC;QAE/E,MAAM,QAAQ,GACZ,sBAAsB,IAAI,cAAc;YACtC,CAAC,CAAC,UAAU,CAAC,kBAAkB,CAAC,qBAAqB,CAAC;gBAClD,YAAY,EAAE,EAAE,MAAM,EAAE,UAAU,EAAE;aACrC,CAAC;YACJ,CAAC,CAAC,UAAU,CAAC,kBAAkB,CAAC,qBAAqB,EAAE,CAAC;QAE5D;;;;;;;;;WASG;QACH,IAAI;YACF,MAAM,KAAK,GAAG,MAAM,QAAQ,EAAE,CAAC;YAC/B,OAAO,+BAA+B,CAAC;gBACrC,WAAW,EAAE,KAAK,CAAC,WAAW;gBAC9B,eAAe,EAAE,KAAK,CAAC,eAAe;gBACtC,KAAK,EAAE,KAAK,CAAC,YAAY;gBACzB,UAAU,EAAE,KAAK,CAAC,UAAU;aAC7B,CAAC,CAAC;SACJ;QAAC,OAAO,KAAK,EAAE;YACd,MAAM,IAAI,qBAAa,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;SACxC;KACF;AACH,CAAC;AAED,SAAS,YAAY,CAAC,IAAY;IAChC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAC9B,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,WAAW,EAAE;QAClD,OAAO,WAAW,CAAC;KACpB;IAED,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC;AAClB,CAAC"}