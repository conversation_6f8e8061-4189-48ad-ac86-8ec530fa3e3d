{"version": 3, "file": "state_machine.js", "sourceRoot": "", "sources": ["../../src/client-side-encryption/state_machine.ts"], "names": [], "mappings": ";;;AAAA,kCAAkC;AAElC,2BAA2B;AAC3B,2BAA2B;AAE3B,kCAMiB;AAEjB,kCAAkD;AAElD,oCAAkE;AAElE,qCAA2C;AAI3C,IAAI,KAAK,GAAoB,IAAI,CAAC;AAClC,SAAS,SAAS;IAChB,IAAI,KAAK,IAAI,IAAI,EAAE;QACjB,MAAM,WAAW,GAAG,IAAA,eAAQ,GAAE,CAAC;QAC/B,IAAI,cAAc,IAAI,WAAW,EAAE;YACjC,MAAM,WAAW,CAAC,YAAY,CAAC;SAChC;QACD,KAAK,GAAG,WAAW,CAAC;KACrB;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,uBAAuB;AACvB,MAAM,oBAAoB,GAAG,CAAC,CAAC;AAC/B,MAAM,kCAAkC,GAAG,CAAC,CAAC;AAC7C,MAAM,kCAAkC,GAAG,CAAC,CAAC;AAC7C,MAAM,8BAA8B,GAAG,CAAC,CAAC;AACzC,MAAM,mCAAmC,GAAG,CAAC,CAAC;AAC9C,MAAM,uBAAuB,GAAG,CAAC,CAAC;AAClC,MAAM,oBAAoB,GAAG,CAAC,CAAC;AAC/B,MAAM,mBAAmB,GAAG,CAAC,CAAC;AAE9B,MAAM,UAAU,GAAG,GAAG,CAAC;AAEvB,MAAM,aAAa,GAAG,IAAI,GAAG,CAAC;IAC5B,CAAC,oBAAoB,EAAE,sBAAsB,CAAC;IAC9C,CAAC,kCAAkC,EAAE,oCAAoC,CAAC;IAC1E,CAAC,kCAAkC,EAAE,oCAAoC,CAAC;IAC1E,CAAC,8BAA8B,EAAE,gCAAgC,CAAC;IAClE,CAAC,mCAAmC,EAAE,qCAAqC,CAAC;IAC5E,CAAC,uBAAuB,EAAE,yBAAyB,CAAC;IACpD,CAAC,oBAAoB,EAAE,sBAAsB,CAAC;IAC9C,CAAC,mBAAmB,EAAE,qBAAqB,CAAC;CAC7C,CAAC,CAAC;AAEH,MAAM,oBAAoB,GAAG;IAC3B,aAAa;IACb,6BAA6B;IAC7B,0BAA0B;IAE1B,+FAA+F;IAC/F,sEAAsE;IACtE,6BAA6B;IAC7B,sCAAsC;CACvC,CAAC;AAEF;;;GAGG;AACH,SAAS,KAAK,CAAC,GAAY;IACzB,IAAI,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE;QACnC,sCAAsC;QACtC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;KACpB;AACH,CAAC;AAkED;;;;GAIG;AACH,MAAa,YAAY;IACvB,YACU,OAA4B,EAC5B,cAAc,IAAA,gCAAyB,EAAC,OAAO,CAAC;QADhD,YAAO,GAAP,OAAO,CAAqB;QAC5B,gBAAW,GAAX,WAAW,CAAqC;IACvD,CAAC;IAEJ;;OAEG;IACH,KAAK,CAAC,OAAO,CACX,QAAgC,EAChC,OAA0B;QAE1B,MAAM,iBAAiB,GAAG,QAAQ,CAAC,kBAAkB,CAAC;QACtD,MAAM,cAAc,GAAG,QAAQ,CAAC,eAAe,CAAC;QAChD,MAAM,cAAc,GAAG,QAAQ,CAAC,eAAe,CAAC;QAChD,MAAM,iBAAiB,GAAG,QAAQ,CAAC,kBAAkB,CAAC;QACtD,MAAM,kBAAkB,GAAG,QAAQ,CAAC,mBAAmB,CAAC;QACxD,IAAI,MAAM,GAAa,IAAI,CAAC;QAE5B,OAAO,OAAO,CAAC,KAAK,KAAK,mBAAmB,IAAI,OAAO,CAAC,KAAK,KAAK,oBAAoB,EAAE;YACtF,KAAK,CAAC,YAAY,OAAO,CAAC,EAAE,KAAK,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC;YAEtF,QAAQ,OAAO,CAAC,KAAK,EAAE;gBACrB,KAAK,kCAAkC,CAAC,CAAC;oBACvC,MAAM,MAAM,GAAG,IAAA,kBAAW,EAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC,CAAC;oBACzD,IAAI,CAAC,cAAc,EAAE;wBACnB,MAAM,IAAI,wBAAe,CACvB,8GAA8G,CAC/G,CAAC;qBACH;oBACD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,cAAc,EAAE,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;oBAEpF,IAAI,QAAQ,EAAE;wBACZ,OAAO,CAAC,yBAAyB,CAAC,QAAQ,CAAC,CAAC;qBAC7C;oBAED,OAAO,CAAC,oBAAoB,EAAE,CAAC;oBAC/B,MAAM;iBACP;gBAED,KAAK,kCAAkC,CAAC,CAAC;oBACvC,MAAM,OAAO,GAAG,OAAO,CAAC,kBAAkB,EAAE,CAAC;oBAC7C,IAAI,CAAC,iBAAiB,EAAE;wBACtB,MAAM,IAAI,wBAAe,CACvB,gHAAgH,CACjH,CAAC;qBACH;oBAED,6EAA6E;oBAC7E,MAAM,aAAa,GAAe,kBAAkB;wBAClD,CAAC,CAAC,MAAM,kBAAkB,CAAC,WAAW,CAClC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,iBAAiB,EAAE,OAAO,CAAC,EAAE,EAAE,OAAO,CAAC,CACpE;wBACH,CAAC,CAAC,MAAM,IAAI,CAAC,WAAW,CAAC,iBAAiB,EAAE,OAAO,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;oBAEnE,OAAO,CAAC,yBAAyB,CAAC,aAAa,CAAC,CAAC;oBACjD,OAAO,CAAC,oBAAoB,EAAE,CAAC;oBAC/B,MAAM;iBACP;gBAED,KAAK,8BAA8B,CAAC,CAAC;oBACnC,MAAM,MAAM,GAAG,OAAO,CAAC,kBAAkB,EAAE,CAAC;oBAC5C,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE,iBAAiB,EAAE,MAAM,CAAC,CAAC;oBAE7E,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;wBACrB,uEAAuE;wBACvE,8EAA8E;wBAC9E,gFAAgF;wBAChF,kFAAkF;wBAClF,8CAA8C;wBAC9C,8FAA8F;wBAC9F,iGAAiG;wBACjG,wFAAwF;wBACxF,sCAAsC;wBACtC,MAAM,GAAG,EAAE,CAAC,EAAE,EAAE,EAAc,CAAC;qBAChC;oBACD,IAAI,KAAK,EAAE,MAAM,GAAG,IAAI,IAAI,EAAE;wBAC5B,OAAO,CAAC,yBAAyB,CAAC,IAAA,gBAAS,EAAC,GAAG,CAAC,CAAC,CAAC;qBACnD;oBAED,OAAO,CAAC,oBAAoB,EAAE,CAAC;oBAE/B,MAAM;iBACP;gBAED,KAAK,mCAAmC,CAAC,CAAC;oBACxC,MAAM,YAAY,GAAG,MAAM,QAAQ,CAAC,oBAAoB,EAAE,CAAC;oBAC3D,OAAO,CAAC,mBAAmB,CAAC,IAAA,gBAAS,EAAC,YAAY,CAAC,CAAC,CAAC;oBACrD,MAAM;iBACP;gBAED,KAAK,uBAAuB,CAAC,CAAC;oBAC5B,MAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;oBACpD,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;oBAE5B,OAAO,CAAC,iBAAiB,EAAE,CAAC;oBAC5B,MAAM;iBACP;gBAED,KAAK,oBAAoB,CAAC,CAAC;oBACzB,MAAM,gBAAgB,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC;oBAC5C,kEAAkE;oBAClE,IAAI,OAAO,CAAC,KAAK,KAAK,oBAAoB,EAAE;wBAC1C,MAAM,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,OAAO,IAAI,oBAAoB,CAAC;wBAC/D,MAAM,IAAI,wBAAe,CAAC,OAAO,CAAC,CAAC;qBACpC;oBACD,MAAM,GAAG,IAAA,kBAAW,EAAC,gBAAgB,EAAE,IAAI,CAAC,OAAO,CAAM,CAAC;oBAC1D,MAAM;iBACP;gBAED;oBACE,MAAM,IAAI,wBAAe,CAAC,kBAAkB,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC;aAChE;SACF;QAED,IAAI,OAAO,CAAC,KAAK,KAAK,oBAAoB,IAAI,MAAM,IAAI,IAAI,EAAE;YAC5D,MAAM,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC;YACvC,IAAI,CAAC,OAAO,EAAE;gBACZ,KAAK,CACH,qHAAqH,CACtH,CAAC;aACH;YACD,MAAM,IAAI,wBAAe,CACvB,OAAO;gBACL,mHAAmH,CACtH,CAAC;SACH;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACH,UAAU,CAAC,OAA6B;QACtC,MAAM,SAAS,GAAG,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC9C,MAAM,IAAI,GAAG,SAAS,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC;QACnF,MAAM,OAAO,GAA2D;YACtE,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC;YAClB,UAAU,EAAE,SAAS,CAAC,CAAC,CAAC;YACxB,IAAI;SACL,CAAC;QACF,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;QAEhC,2FAA2F;QAC3F,6FAA6F;QAC7F,OAAO,IAAI,OAAO,CAAC,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,EAAE;YAC3C,MAAM,MAAM,GAAG,IAAI,kBAAU,EAAE,CAAC;YAEhC,wCAAwC;YACxC,IAAI,MAAkB,CAAC;YACvB,IAAI,SAAqB,CAAC;YAE1B,SAAS,cAAc;gBACrB,KAAK,MAAM,IAAI,IAAI,CAAC,MAAM,EAAE,SAAS,CAAC,EAAE;oBACtC,IAAI,IAAI,EAAE;wBACR,IAAI,CAAC,kBAAkB,EAAE,CAAC;wBAC1B,IAAI,CAAC,OAAO,EAAE,CAAC;qBAChB;iBACF;YACH,CAAC;YAED,SAAS,SAAS;gBAChB,cAAc,EAAE,CAAC;gBACjB,MAAM,CAAC,IAAI,wBAAe,CAAC,uBAAuB,CAAC,CAAC,CAAC;YACvD,CAAC;YAED,SAAS,OAAO,CAAC,GAAU;gBACzB,cAAc,EAAE,CAAC;gBACjB,MAAM,OAAO,GAAG,IAAI,wBAAe,CAAC,oBAAoB,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;gBAC1E,MAAM,CAAC,OAAO,CAAC,CAAC;YAClB,CAAC;YAED,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,SAAS,EAAE;gBACpE,SAAS,GAAG,GAAG,CAAC,OAAO,CAAC;oBACtB,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,SAAS;oBACzC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,SAAS,IAAI,IAAI;iBAClD,CAAC,CAAC;gBAEH,SAAS,CAAC,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;gBACnC,SAAS,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;gBAC/B,IAAI;oBACF,8DAA8D;oBAC9D,MAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,CAA4B,CAAC;oBAC5D,MAAM,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;oBACxC,KAAK,KAAK,SAAS,EAAE,CAAC;oBACtB,OAAO,CAAC,MAAM,GAAG,CACf,MAAM,KAAK,CAAC,WAAW,CAAC,gBAAgB,CAAC;wBACvC,eAAe,EAAE,SAAS;wBAC1B,OAAO,EAAE,SAAS;wBAClB,WAAW,EAAE,EAAE,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,IAAI,EAAE;wBACvD,KAAK,EAAE;4BACL,4DAA4D;4BAC5D,IAAI,EAAE,iBAAiB;4BACvB,IAAI,EAAE,CAAC;4BACP,IAAI,EAAE,CAAC;4BACP,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,aAAa;4BAC/C,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,aAAa;yBAClD;qBACF,CAAC,CACH,CAAC,MAAM,CAAC;iBACV;gBAAC,OAAO,GAAG,EAAE;oBACZ,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC;iBACrB;aACF;YAED,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC;YAC3C,IAAI,UAAU,EAAE;gBACd,MAAM,WAAW,GAAG,OAAO,CAAC,WAA8C,CAAC;gBAC3E,MAAM,kBAAkB,GAAG,UAAU,CAAC,WAAW,CAAC,CAAC;gBACnD,IAAI,kBAAkB,EAAE;oBACtB,MAAM,KAAK,GAAG,IAAI,CAAC,kBAAkB,CAAC,WAAW,EAAE,kBAAkB,CAAC,CAAC;oBACvE,IAAI,KAAK;wBAAE,MAAM,CAAC,KAAK,CAAC,CAAC;oBACzB,IAAI;wBACF,MAAM,IAAI,CAAC,aAAa,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAC;qBACvD;oBAAC,OAAO,KAAK,EAAE;wBACd,OAAO,OAAO,CAAC,KAAK,CAAC,CAAC;qBACvB;iBACF;aACF;YACD,MAAM,GAAG,GAAG,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,EAAE;gBACjC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC;YAEH,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;YAClC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAE9B,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE;gBACvB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gBACpB,OAAO,OAAO,CAAC,WAAW,GAAG,CAAC,IAAI,MAAM,CAAC,MAAM,EAAE;oBAC/C,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;oBACjE,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;iBAC/C;gBAED,IAAI,OAAO,CAAC,WAAW,IAAI,CAAC,EAAE;oBAC5B,sEAAsE;oBACtE,cAAc,EAAE,CAAC;oBACjB,OAAO,EAAE,CAAC;iBACX;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED,CAAC,QAAQ,CAAC,OAA0B;QAClC,KACE,IAAI,OAAO,GAAG,OAAO,CAAC,cAAc,EAAE,EACtC,OAAO,IAAI,IAAI,EACf,OAAO,GAAG,OAAO,CAAC,cAAc,EAAE,EAClC;YACA,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;SAChC;IACH,CAAC;IAED;;;;;;;OAOG;IACH,kBAAkB,CAChB,WAAmB,EACnB,UAAsC;QAEtC,MAAM,cAAc,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC/C,KAAK,MAAM,MAAM,IAAI,oBAAoB,EAAE;YACzC,IAAI,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;gBACnC,OAAO,IAAI,wBAAe,CAAC,uCAAuC,WAAW,KAAK,MAAM,EAAE,CAAC,CAAC;aAC7F;SACF;IACH,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,aAAa,CACjB,UAAsC,EACtC,OAA8B;QAE9B,IAAI,UAAU,CAAC,qBAAqB,EAAE;YACpC,MAAM,IAAI,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,qBAAqB,CAAC,CAAC;YACjE,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,GAAG,GAAG,IAAI,CAAC;SACnC;QACD,IAAI,UAAU,CAAC,SAAS,EAAE;YACxB,OAAO,CAAC,EAAE,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;SACtD;QACD,IAAI,UAAU,CAAC,6BAA6B,EAAE;YAC5C,OAAO,CAAC,UAAU,GAAG,UAAU,CAAC,6BAA6B,CAAC;SAC/D;IACH,CAAC;IAED;;;;;;;;;;OAUG;IACH,KAAK,CAAC,mBAAmB,CACvB,MAAmB,EACnB,EAAU,EACV,MAAgB;QAEhB,MAAM,EAAE,EAAE,EAAE,GAAG,kCAA0B,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;QAEzD,MAAM,WAAW,GAAG,MAAM,MAAM;aAC7B,EAAE,CAAC,EAAE,CAAC;aACN,eAAe,CAAC,MAAM,EAAE;YACvB,YAAY,EAAE,KAAK;YACnB,aAAa,EAAE,KAAK;SACrB,CAAC;aACD,OAAO,EAAE,CAAC;QAEb,MAAM,IAAI,GAAG,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAA,gBAAS,EAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QACvE,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;;;OAOG;IACH,KAAK,CAAC,WAAW,CAAC,MAAmB,EAAE,EAAU,EAAE,OAAmB;QACpE,MAAM,OAAO,GAAG,EAAE,YAAY,EAAE,KAAK,EAAE,aAAa,EAAE,KAAK,EAAE,CAAC;QAC9D,MAAM,EAAE,EAAE,EAAE,GAAG,kCAA0B,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;QACzD,MAAM,UAAU,GAAG,IAAA,kBAAW,EAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAEjD,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAElE,OAAO,IAAA,gBAAS,EAAC,QAAQ,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;IAC/C,CAAC;IAED;;;;;;;OAOG;IACH,SAAS,CACP,MAAmB,EACnB,iBAAyB,EACzB,MAAkB;QAElB,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,UAAU,EAAE,cAAc,EAAE,GAC9C,kCAA0B,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;QAE3D,OAAO,MAAM;aACV,EAAE,CAAC,MAAM,CAAC;aACV,UAAU,CAAU,cAAc,EAAE,EAAE,WAAW,EAAE,EAAE,KAAK,EAAE,UAAU,EAAE,EAAE,CAAC;aAC3E,IAAI,CAAC,IAAA,kBAAW,EAAC,MAAM,CAAC,CAAC;aACzB,OAAO,EAAE,CAAC;IACf,CAAC;CACF;AAhXD,oCAgXC"}