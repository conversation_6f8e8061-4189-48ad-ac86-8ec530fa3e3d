"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const zlib_1 = require("zlib");
exports.default = (0, zlib_1.gunzipSync)(Buffer.from('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', 'base64'));
//# sourceMappingURL=code-points-data.js.map