const axios = require("axios");
const fs = require("fs");
const path = require("path");

const uploadFile = async () => {
  try {
    // Path to the file to be uploaded
    const folderPath = "./uploads"; // Replace with your folder path
    const fileName = "366-1200x1200.jpg"; // Replace with your file name
    const filePath = path.join(__dirname, fileName);

    // Check if the file exists
    if (!fs.existsSync(filePath)) {
      throw new Error(`File not found at path: ${filePath}`);
    }

    // Read the file as a binary stream
    const fileStream = fs.createReadStream(filePath);

    // API endpoint and headers
    const apiUrl =
      "https://api.businessmessaging.jio.com/v1/messaging/upload/files"; // Replace with your API URL
    const headers = {
      "Content-Type": "image/jpeg", // Replace with the correct MIME type for your file
      Authorization: "Bearer ZWUYY2VLNMYTOGM4MY0ZNMY2LWI4NMYTYTLHYJZJZTI2ODUW", // Replace with your actual token
    };

    console.log("Uploading file...");

    // Make the POST request with Axios
    const response = await axios.post(apiUrl, fileStream, { headers });

    console.log("File uploaded successfully!");
    console.log("Response:", response.data);
  } catch (error) {
    console.error("Error uploading file:", error.message);
    if (error.response) {
      console.error("Response:", error.response.data);
    }
  }
};

// Call the function
uploadFile();
