[{"subject": "Your WhatsApp Business account messaging limit increase request denied", "html": "<p>We regret to inform you that your request to increase the messaging limit for your WhatsApp Business account <span class='highlight' style='font-weight: bold;'>{{businessName}}</span> has been denied based on the recent review of your account activity.</p><div class='details'><p><strong>Details:</strong></p><ul><li><strong>Description:</strong> {{alertDescription}}</li></ul></div><p>This decision may be due to compliance issues or activity that does not align with WhatsApp’s Business Terms of Service. We strongly recommend reviewing your account practices to ensure they meet the necessary guidelines.</p>", "platform": "chatomate", "type": "account_alerts", "subtype": "INCREASED_CAPABILITIES_ELIGIBILITY_FAILED", "header": "<html><head><title>waba/template notifications</title></head><body style='font-family: Arial, Helvetica, sans-serif; background-color: #edf2f6; color: black; font-size: 16px; padding-top:20px'><div class='template' style='padding: 20px 0px 0px; border-radius: 10px; background-color: #ffffff; width: 650px; margin: 20px auto;'><img class='logo' src='{{logo}}' alt='Logo' style='width: auto; height: 75px; display: block; margin: 20px auto;'><div class='mainText' style='margin: 30px; padding: 0px 30px;'><p><strong>Alert!</strong></p><p style='font-size: large;'>Dear {{userName}},</p>"}, {"subject": "Your WhatsApp Business account messaging limit increase request denied", "html": "<p>We regret to inform you that your request to increase the messaging limit for your WhatsApp Business account <span class='highlight' style='font-weight: bold'>{{businessName}}</span> has been denied following a review of your recent account activity.</p><div class='details'><p><strong>Details:</strong></p><ul><li><strong>Description:</strong> {{alertDescription}}</li></ul></div><p>This decision may be based on your current usage patterns. We encourage you to actively use the WhatsApp Business platform over the coming days. Regular and consistent engagement with your audience can help demonstrate the need for higher messaging limits.</p>", "platform": "chatomate", "type": "account_alerts", "subtype": "INCREASED_CAPABILITIES_ELIGIBILITY_DEFERRED", "header": "<html><head><title>waba/template notifications</title></head><body style='font-family: Arial, Helvetica, sans-serif; background-color: #edf2f6; color: black; font-size: 16px; padding-top:20px'><div class='template' style='padding: 20px 0px 0px; border-radius: 10px; background-color: #ffffff; width: 650px; margin: 20px auto;'><img class='logo' src='{{logo}}' alt='Logo' style='width: auto; height: 75px; display: block; margin: 20px auto;'><div class='mainText' style='margin: 30px; padding: 0px 30px;'><p><strong>Alert!</strong></p><p style='font-size: large;'>Dear {{userName}},</p>"}, {"subject": "Your WhatsApp Business account messaging limit increase request denied", "html": "<p>We regret to inform you that your request to increase the messaging limit for your WhatsApp Business account <span class='highlight' style='font-weight: bold'>{{businessName}}</span> has been denied following a review of your recent account activity.</p><div class='details'><p><strong>Details:</strong></p><ul><li><strong>Description:</strong> {{alertDescription}}</li></ul></div><p>Required more information. Please complete the verification in Meta for Business, or actively use the WhatsApp Business platform for several days and follow our messaging policies.</p>", "platform": "chatomate", "type": "account_alerts", "subtype": "INCREASED_CAPABILITIES_ELIGIBILITY_NEED_MORE_INFO", "header": "<html><head><title>waba/template notifications</title></head><body style='font-family: Arial, Helvetica, sans-serif; background-color: #edf2f6; color: black; font-size: 16px; padding-top:20px'><div class='template' style='padding: 20px 0px 0px; border-radius: 10px; background-color: #ffffff; width: 650px; margin: 20px auto;'><img class='logo' src='{{logo}}' alt='Logo' style='width: auto; height: 75px; display: block; margin: 20px auto;'><div class='mainText' style='margin: 30px; padding: 0px 30px;'><p><strong>Alert!</strong></p><p style='font-size: large;'>Dear {{userName}},</p>"}, {"subject": "Your WhatsApp official business account request has been approved", "html": "<p>We are pleased to inform you that your account is now officially recognized as a business account.</p><p>To enhance your presence on WhatsApp, make sure to update your business profile with a profile picture, email address, website, and business description. You can edit these details at any time to keep your account authentic and engaging for your audience.</p>", "platform": "chatomate", "type": "account_alerts", "subtype": "OBA_APPROVED", "header": "<html><head><title>waba/template notifications</title></head><body style='font-family: Arial, Helvetica, sans-serif; background-color: #edf2f6; color: black; font-size: 16px; padding-top:20px'><div class='template' style='padding: 20px 0px 0px; border-radius: 10px; background-color: #ffffff; width: 650px; margin: 20px auto;'><img class='logo' src='{{logo}}' alt='Logo' style='width: auto; height: 75px; display: block; margin: 20px auto;'><div class='mainText' style='margin: 30px; padding: 0px 30px;'><p><strong>Good News!</strong></p><p style='font-size: large;'>Dear {{userName}},</p>"}, {"subject": "Your WhatsApp Business account review is complete", "html": "<p>We are pleased to inform you that your WhatsApp Business account <span class='highlight' style='font-weight: bold;'>{{wabaName}}</span> has been successfully reviewed and approved.</p><p>We appreciate your patience during this review process and look forward to supporting your business growth.</p>", "platform": "chatomate", "type": "account_review_update", "subtype": "APPROVED", "header": "<html><head><title>waba/template notifications</title></head><body style='font-family: Arial, Helvetica, sans-serif; background-color: #edf2f6; color: black; font-size: 16px; padding-top:20px'><div class='template' style='padding: 20px 0px 0px; border-radius: 10px; background-color: #ffffff; width: 650px; margin: 20px auto;'><img class='logo' src='{{logo}}' alt='Logo' style='width: auto; height: 75px; display: block; margin: 20px auto;'><div class='mainText' style='margin: 30px; padding: 0px 30px;'><p style='font-size: large;'>Dear {{userName}},</p>"}, {"subject": "Your WhatsApp Business account review is complete", "html": "<p>We regret to inform you that after a thorough review, your WhatsApp Business account, <span class='highlight' style='font-weight: bold;'>{{wabaName}}</span>, has been rejected. Unfortunately, this means that you will not be able to use the WhatsApp Business platform with this account.</p><p>We encourage you to review the terms and take necessary actions.</p>", "platform": "chatomate", "type": "account_review_update", "subtype": "REJECTED", "header": "<html><head><title>waba/template notifications</title></head><body style='font-family: Arial, Helvetica, sans-serif; background-color: #edf2f6; color: black; font-size: 16px; padding-top:20px'><div class='template' style='padding: 20px 0px 0px; border-radius: 10px; background-color: #ffffff; width: 650px; margin: 20px auto;'><img class='logo' src='{{logo}}' alt='Logo' style='width: auto; height: 75px; display: block; margin: 20px auto;'><div class='mainText' style='margin: 30px; padding: 0px 30px;'><p style='font-size: large;'>Dear {{userName}},</p>"}, {"subject": "Your WhatsApp Business account has been restricted", "html": "<p>We regret to inform you that the WhatsApp Business account <span class='highlight' style='font-weight: bold;'>{{wabaName}}</span> has been restricted due to activities that violate WhatsApp's <a href='https://www.whatsapp.com/legal/business-terms'>Business Terms of Service</a> regarding the sending of spam.</p><p><strong>Effective Until:</strong> {{expirationTimestamp}}</p><p><strong>Account Restrictions:</strong></p><ul><li>Can't have phone numbers added to it</li><li>Can't initiate conversations with customers</li><li>Can't respond to messages from customers</li></ul><p>We encourage you to review the terms and take necessary actions to address these violations.</p><hr /><p><strong>What you can do:</strong></p><p>Make sure that the account does not send spam and only messages people who have opted in to receive messages from it on WhatsApp. Learn more about <a href='https://faq.whatsapp.com/general/whatsapp-business-api/how-to-comply-with-the-whatsapp-business-and-commerce-policies'>how to comply with our policies</a>.</p>", "platform": "chatomate", "type": "account_update", "subtype": "ACCOUNT_RESTRICTION", "header": "<html><head><title>waba/template notifications</title></head><body style='font-family: Arial, Helvetica, sans-serif; background-color: #edf2f6; color: black; font-size: 16px; padding-top:20px'><div class='template' style='padding: 20px 0px 0px; border-radius: 10px; background-color: #ffffff; width: 650px; margin: 20px auto;'><img class='logo' src='{{logo}}' alt='Logo' style='width: auto; height: 75px; display: block; margin: 20px auto;'><div class='mainText' style='margin: 30px; padding: 0px 30px;'><p style='font-size: large;'>Dear {{userName}},</p>"}, {"subject": "Your WhatsApp Business account has been restricted", "html": "<p>We regret to inform you that your WhatsApp Business account, <span class='highlight' style='font-weight: bold'>{{wabaName}}</span>, has been disabled on <span class='highlight' style='font-weight: bold'>{{wabaBanDate}}</span>.</p><p><strong>Disabled accounts:</strong></p><ul><li>Can't receive messages</li><li>Can't respond to messages from customers</li><li>Can't initiate conversations with customers</li><li>Can't have phone numbers added to it</ul><p>We strongly encourage you to review WhatsApp’s Business Terms of Service and take the necessary actions to address these violations to restore your account.</p>", "platform": "chatomate", "type": "account_update", "subtype": "DISABLED_UPDATE", "header": "<html><head><title>waba/template notifications</title></head><body style='font-family: Arial, Helvetica, sans-serif; background-color: #edf2f6; color: black; font-size: 16px; padding-top:20px'><div class='template' style='padding: 20px 0px 0px; border-radius: 10px; background-color: #ffffff; width: 650px; margin: 20px auto;'><img class='logo' src='{{logo}}' alt='Logo' style='width: auto; height: 75px; display: block; margin: 20px auto;'><div class='mainText' style='margin: 30px; padding: 0px 30px;'><p style='font-size: large;'>Dear {{userName}},</p>"}, {"subject": "Your WhatsApp Business account is violating policy", "html": "<p>We regret to inform you that the WhatsApp Business account <span class='highlight' style='font-weight: bold'>{{wabaName}}</span> has been identified as sending spam.</p><p>This activity does not comply with <a href='https://www.whatsapp.com/legal/business-terms/'>WhatsApp's Business Terms of Service</a>. If this continues to be a policy issue, the account may face <a href='https://developers.facebook.com/docs/whatsapp/overview/policy-enforcement/'>restrictions or be disabled</a>.</p><p>We encourage you to review the terms and take necessary actions to address these violations.</p><hr /><div class='details'><p><strong>What you can do:</strong></p>Make sure that the account does not send spam and only messages people who have opted in to receive messages from it on WhatsApp. Learn more about <a href='https://faq.whatsapp.com/general/whatsapp-business-api/how-to-comply-with-the-whatsapp-business-and-commerce-policies'>how to comply with our policies</a>.</div>", "platform": "chatomate", "type": "account_update", "subtype": "ACCOUNT_VIOLATION", "header": "<html><head><title>waba/template notifications</title></head><body style='font-family: Arial, Helvetica, sans-serif; background-color: #edf2f6; color: black; font-size: 16px; padding-top:20px'><div class='template' style='padding: 20px 0px 0px; border-radius: 10px; background-color: #ffffff; width: 650px; margin: 20px auto;'><img class='logo' src='{{logo}}' alt='Logo' style='width: auto; height: 75px; display: block; margin: 20px auto;'><div class='mainText' style='margin: 30px; padding: 0px 30px;'><p><strong>Account activity issue</strong></p><p style='font-size: large;'>Dear {{userName}},</p>"}, {"subject": "Your WhatsApp Business account has been deleted", "html": "<p>We regret to inform you that your WhatsApp Business account<strong>{{wabaName}}</strong> has been deleted.</p><p>This action may impact your ongoing operations, as you will no longer have access to your templates, conversations, or any data associated with this account.</p>", "platform": "chatomate", "type": "account_update", "subtype": "ACCOUNT_DELETED", "header": "<html><head><title>waba/template notifications</title></head><body style='font-family: Arial, Helvetica, sans-serif; background-color: #edf2f6; color: black; font-size: 16px; padding-top: 20px;'><div class='template' style='padding: 20px 0px 0px; border-radius: 10px; background-color: #ffffff; width: 690px; margin: 20px auto;'><img class='logo' src='{{logo}}' alt='Logo' style='width: auto; height: 75px; display: block; margin: 20px auto;' /><div class='mainText' style='margin: 30px; padding: 0px 30px;'><p><strong>Account Alert!</strong></p><p style='font-size: large;'>Dear {{userName}},</p>"}, {"subject": "Your WhatsApp Business limits have increased", "html": "<p>We are pleased to inform you that the limits for your business activities with <span class='highlight' style='font-weight: bold'>{{businessName}}</span> have been increased. You can now enjoy the following enhanced capabilities:</p><ul><li><strong>1000 Business-Initiated Conversations:</strong><br />Per phone number in a rolling 24-hour period.</li><li><strong>Additional Phone Numbers</strong></li><li><strong>More WhatsApp Business Accounts</strong></li></ul><p>As a reminder, you'll always get unlimited customer-initiated conversations.</p>", "platform": "chatomate", "type": "business_capability_update", "subtype": "", "header": "<html><head><title>waba/template notifications</title></head><body style='font-family: Arial, Helvetica, sans-serif; background-color: #edf2f6; color: black; font-size: 16px; padding-top: 20px;'><div class='template' style='padding: 20px 0px 0px; border-radius: 10px; background-color: #ffffff; width: 690px; margin: 20px auto;'><img class='logo' src='{{logo}}' alt='Logo' style='width: auto; height: 75px; display: block; margin: 20px auto;' /><div class='mainText' style='margin: 30px; padding: 0px 30px;'><p><strong>Good news!</strong></p><p style='font-size: large;'>Dear {{userName}},</p>"}, {"subject": "Your display name has been approved", "html": "<p>We are pleased to inform you that your display name <span class='highlight' style='font-weight: bold'>{{displayName}}</span> for your WhatsApp Business account <span class='highlight' style='font-weight: bold'>{{wabaName}}</span> has been successfully approved.</p><p>Your customers will now see the updated display name, and you may proceed with sending messages under this newly approved identity. This approval ensures that your brand is appropriately represented in all communications.</p>", "platform": "chatomate", "type": "phone_number_name_update", "subtype": "APPROVED", "header": "<html><head><title>waba/template notifications</title></head><body style='font-family: Arial, Helvetica, sans-serif; background-color: #edf2f6; color: black; font-size: 16px; padding-top: 20px;'><div class='template' style='padding: 20px 0px 0px; border-radius: 10px; background-color: #ffffff; width: 690px; margin: 20px auto;'><img class='logo' src='{{logo}}' alt='Logo' style='width: auto; height: 75px; display: block; margin: 20px auto;' /><div class='mainText' style='margin: 30px; padding: 0px 30px;'><p style='font-size: large;'>Dear {{userName}},</p>"}, {"subject": "Your display name wasn't approved", "html": "<p>This WhatsApp Business account can't use <span class='highlight' style='font-weight: bold'>{{displayName}}</span> as its display name because <span class='highlight' style='font-weight: bold'>{{rejectionReason}}</span>.</p><p>See <a href='https://developers.facebook.com/docs/whatsapp/guides/display-name' target='_blank'>best practices for display names</a>.</p><p>As a result, your customers will not see this display name, and you will need to submit a revised name for approval.</p>", "platform": "chatomate", "type": "phone_number_name_update", "subtype": "REJECTED", "header": "<html><head><title>waba/template notifications</title></head><body style='font-family: Arial, Helvetica, sans-serif; background-color: #edf2f6; color: black; font-size: 16px; padding-top: 20px;'><div class='template' style='padding: 20px 0px 0px; border-radius: 10px; background-color: #ffffff; width: 690px; margin: 20px auto;'><img class='logo' src='{{logo}}' alt='Logo' style='width: auto; height: 75px; display: block; margin: 20px auto;' /><div class='mainText' style='margin: 30px; padding: 0px 30px;'><p style='font-size: large;'>Dear {{userName}},</p>"}, {"subject": "Your WhatsApp phone number status has changed", "html": "<p>We are writing to inform you that the status of the phone number <span class='highlight' style='font-weight: bold'>{{phoneNumber}}</span> associated with your WhatsApp Business account <span class='highlight' style='font-weight: bold'>{{wabaName}}</span> has been updated to <span class='highlight' style='font-weight: bold'>Pending</span>.</p><p>This status indicates that the phone number is currently awaiting further action or verification.</p>", "platform": "chatomate", "type": "phone_number_quality_update", "subtype": "PENDING", "header": "<html><head><title>waba/template notifications</title></head><body style='font-family: Arial, Helvetica, sans-serif; background-color: #edf2f6; color: black; font-size: 16px; padding-top: 20px;'><div class='template' style='padding: 20px 0px 0px; border-radius: 10px; background-color: #ffffff; width: 690px; margin: 20px auto;'><img class='logo' src='{{logo}}' alt='Logo' style='width: auto; height: 75px; display: block; margin: 20px auto;' /><div class='mainText' style='margin: 30px; padding: 0px 30px;'><p style='font-size: large;'>Dear {{userName}},</p>"}, {"subject": "Your WhatsApp phone number status has changed", "html": "<p>We are writing to inform you that the status of the phone number <span class='highlight' style='font-weight: bold;'>{{phoneNumber}}</span> associated with your WhatsApp Business account <span class='highlight' style='font-weight: bold;'>{{wabaName}}</span> has been <span class='highlight' style='font-weight: bold;'>Deleted</span>.</p><p>This status change means the phone number is no longer active and may impact your ability to send or receive messages through your WhatsApp Business account.</p>", "platform": "chatomate", "type": "phone_number_quality_update", "subtype": "DELETED", "header": "<html><head><title>waba/template notifications</title></head><body style='font-family: Arial, Helvetica, sans-serif; background-color: #edf2f6; color: black; font-size: 16px; padding-top: 20px;'><div class='template' style='padding: 20px 0px 0px; border-radius: 10px; background-color: #ffffff; width: 690px; margin: 20px auto;'><img class='logo' src='{{logo}}' alt='Logo' style='width: auto; height: 75px; display: block; margin: 20px auto;' /><div class='mainText' style='margin: 30px; padding: 0px 30px;'><p><strong>Alert!</strong></p><p style='font-size: large;'>Dear {{userName}},</p>"}, {"subject": "Your WhatsApp phone number status has changed", "html": "<p>We are writing to inform you that the status of the phone number <span class='highlight' style='font-weight: bold'>{{phoneNumber}}</span> associated with your WhatsApp Business account <span class='highlight' style='font-weight: bold'>{{wabaName}}</span> has been <span class='highlight' style='font-weight: bold'>Migrated</span>.</p>", "platform": "chatomate", "type": "phone_number_quality_update", "subtype": "MIGRATED", "header": "<html><head><title>waba/template notifications</title></head><body style='font-family: Arial, Helvetica, sans-serif; background-color: #edf2f6; color: black; font-size: 16px; padding-top: 20px;'><div class='template' style='padding: 20px 0px 0px; border-radius: 10px; background-color: #ffffff; width: 690px; margin: 20px auto;'><img class='logo' src='{{logo}}' alt='Logo' style='width: auto; height: 75px; display: block; margin: 20px auto;' /><div class='mainText' style='margin: 30px; padding: 0px 30px;'><p style='font-size: large;'>Dear {{userName}},</p>"}, {"subject": "Your WhatsApp phone number status has changed", "html": "<p>We regret to inform you that the status of the phone number <span class='highlight' style='font-weight: bold;'>{{phoneNumber}}</span> associated with your WhatsApp Business account <span class='highlight' style='font-weight: bold;'>{{wabaName}}</span> has been changed to <span class='highlight' style='font-weight: bold;'>Banned</span>.</p><p>This number's daily messaging limit will be reduced if these reports do not decrease within seven days. Learn more about <a href='https://developers.facebook.com/docs/whatsapp/api/rate-limits/quality-limits'>phone number quality ratings.</a></p>", "platform": "chatomate", "type": "phone_number_quality_update", "subtype": "BANNED", "header": "<html><head><title>waba/template notifications</title></head><body style='font-family: Arial, Helvetica, sans-serif; background-color: #edf2f6; color: black; font-size: 16px; padding-top: 20px;'><div class='template' style='padding: 20px 0px 0px; border-radius: 10px; background-color: #ffffff; width: 690px; margin: 20px auto;'><img class='logo' src='{{logo}}' alt='Logo' style='width: auto; height: 75px; display: block; margin: 20px auto;' /><div class='mainText' style='margin: 30px; padding: 0px 30px;'><p style='font-size: large;'>Dear {{userName}},</p>"}, {"subject": "Your WhatsApp phone number status has changed", "html": "<p>We regret to inform you that the status of the phone number <span class='highlight' style='font-weight: bold'>{{phoneNumber}}</span> associated with your WhatsApp Business account <span class='highlight' style='font-weight: bold'>{{wabaName}}</span> has been changed to <span class='highlight' style='font-weight: bold'>Restricted</span>.</p><p>This status may impact your ability to send or receive messages and affect your message limits. Please review your account activity and ensure compliance with WhatsApp’s guidelines.</p>", "platform": "chatomate", "type": "phone_number_quality_update", "subtype": "RESTRICTED", "header": "<html><head><title>waba/template notifications</title></head><body style='font-family: Arial, Helvetica, sans-serif; background-color: #edf2f6; color: black; font-size: 16px; padding-top: 20px;'><div class='template' style='padding: 20px 0px 0px; border-radius: 10px; background-color: #ffffff; width: 690px; margin: 20px auto;'><img class='logo' src='{{logo}}' alt='Logo' style='width: auto; height: 75px; display: block; margin: 20px auto;' /><div class='mainText' style='margin: 30px; padding: 0px 30px;'><p><strong>Alert!</strong></p><p style='font-size: large;'>Dear {{userName}},</p>"}, {"subject": "Your WhatsApp phone number status has changed", "html": "<p>The messaging limit for phone number <span class='highlight' style='font-weight: bold;'>{{phoneNumber}}</span> from your WhatsApp business account <span class='highlight' style='font-weight: bold;'>{{wabaName}}</span> has increased.</p><p>You can now send messages to <span class='highlight' style='font-weight: bold;'>{{currentLimit}}</span> customers per day. <a href='https://developers.facebook.com/docs/whatsapp/messaging-limits#quality-rating-and-messaging-limits'>Learn More</a></p>", "platform": "chatomate", "type": "phone_number_quality_update", "subtype": "RATE_LIMITED", "header": "<html><head><title>waba/template notifications</title></head><body style='font-family: Arial, Helvetica, sans-serif; background-color: #edf2f6; color: black; font-size: 16px; padding-top: 20px;'><div class='template' style='padding: 20px 0px 0px; border-radius: 10px; background-color: #ffffff; width: 690px; margin: 20px auto;'><img class='logo' src='{{logo}}' alt='Logo' style='width: auto; height: 75px; display: block; margin: 20px auto;' /><div class='mainText' style='margin: 30px; padding: 0px 30px;'><p style='font-size: large;'>Dear {{userName}},</p>"}, {"subject": "Your WhatsApp phone number status has changed", "html": "<p>We regret to inform you that the status of the phone number <span class='highlight' style='font-weight: bold;'>{{phoneNumber}}</span> associated with your WhatsApp Business account <span class='highlight' style='font-weight: bold;'>{{wabaName}}</span> has been changed to <span class='highlight' style='font-weight: bold;'>Flagged</span>.</p><p>This number's daily messaging limit will be reduced if these reports do not decrease within seven days. Learn more about <a href='https://developers.facebook.com/docs/whatsapp/api/rate-limits/quality-limits'>phone number quality ratings.</a></p>", "platform": "chatomate", "type": "phone_number_quality_update", "subtype": "FLAGGED", "header": "<html><head><title>waba/template notifications</title></head><body style='font-family: Arial, Helvetica, sans-serif; background-color: #edf2f6; color: black; font-size: 16px; padding-top: 20px;'><div class='template' style='padding: 20px 0px 0px; border-radius: 10px; background-color: #ffffff; width: 690px; margin: 20px auto;'><img class='logo' src='{{logo}}' alt='Logo' style='width: auto; height: 75px; display: block; margin: 20px auto;' /><div class='mainText' style='margin: 30px; padding: 0px 30px;'><p style='font-size: large;'>Dear {{userName}},</p>"}, {"subject": "Your WhatsApp phone number status has changed", "html": "<p>We are pleased to inform you that the status of the phone number <span class='highlight' style='font-weight: bold'>{{phoneNumber}}</span> associated with your WhatsApp Business account <span class='highlight' style='font-weight: bold'>{{wabaName}}</span> has been changed to <span class='highlight' style='font-weight: bold'>Connected</span>.</p><p>This update means your phone number is now fully operational and ready for use with your WhatsApp Business account. You can resume all messaging activities and engage with your customers without interruption.</p>", "platform": "chatomate", "type": "phone_number_quality_update", "subtype": "CONNECTED", "header": "<html><head><title>waba/template notifications</title></head><body style='font-family: Arial, Helvetica, sans-serif; background-color: #edf2f6; color: black; font-size: 16px; padding-top: 20px;'><div class='template' style='padding: 20px 0px 0px; border-radius: 10px; background-color: #ffffff; width: 690px; margin: 20px auto;'><img class='logo' src='{{logo}}' alt='Logo' style='width: auto; height: 75px; display: block; margin: 20px auto;' /><div class='mainText' style='margin: 30px; padding: 0px 30px;'><p><strong>Good news!</strong></p><p style='font-size: large;'>Dear {{userName}},</p>"}, {"subject": "Your WhatsApp phone number status has changed", "html": "<p>We are writing to inform you that the status of the phone number <span class='highlight' style='font-weight: bold;'>{{phoneNumber}}</span> associated with your WhatsApp Business account <span class='highlight' style='font-weight: bold;'>{{wabaName}}</span> has been <span class='highlight' style='font-weight: bold;'>Disconnect</span>.</p><p>This status change means the phone number is no longer active and may impact your ability to send or receive messages through your WhatsApp Business account.</p>", "platform": "chatomate", "type": "phone_number_quality_update", "subtype": "DISCONNECTED", "header": "<html><head><title>waba/template notifications</title></head><body style='font-family: Arial, Helvetica, sans-serif; background-color: #edf2f6; color: black; font-size: 16px; padding-top: 20px;'><div class='template' style='padding: 20px 0px 0px; border-radius: 10px; background-color: #ffffff; width: 690px; margin: 20px auto;'><img class='logo' src='{{logo}}' alt='Logo' style='width: auto; height: 75px; display: block; margin: 20px auto;' /><div class='mainText' style='margin: 30px; padding: 0px 30px;'><p><strong>Alert!</strong></p><p style='font-size: large;'>Dear {{userName}},</p>"}, {"subject": "Your WhatsApp phone number status has changed", "html": "<p>We are writing to inform you that the status of the phone number <span style='font-weight: bold'>{{phoneNumber}}</span> associated with your WhatsApp Business account <span style='font-weight: bold'>{{wabaName}}</span> has been changed to <span style='font-weight: bold'>Unknown</span>.</p><p>This status may impact your ability to send or receive messages. Please review your account activity and ensure compliance with WhatsApp’s guidelines.</p>", "platform": "chatomate", "type": "phone_number_quality_update", "subtype": "UNKNOWN", "header": "<html><head><title>waba/template notifications</title></head><body style='font-family: Arial, Helvetica, sans-serif; background-color: #edf2f6; color: black; font-size: 16px; padding-top: 20px;'><div class='template' style='padding: 20px 0px 0px; border-radius: 10px; background-color: #ffffff; width: 690px; margin: 20px auto;'><img class='logo' src='{{logo}}' alt='Logo' style='width: auto; height: 75px; display: block; margin: 20px auto;' /><div class='mainText' style='margin: 30px; padding: 0px 30px;'><p><strong>Alert!</strong></p><p style='font-size: large;'>Dear {{userName}},</p>"}, {"subject": "Your WhatsApp phone number status has changed", "html": "<p>We are writing to inform you that the status of the phone number <span style='font-weight: bold'>{{phoneNumber}}</span> associated with your WhatsApp Business account <span style='font-weight: bold'>{{wabaName}}</span> has been changed to <span style='font-weight: bold'>Unverified</span>.</p><p>This status may impact your ability to send or receive messages. Please review your account activity and ensure compliance with WhatsApp’s guidelines.</p>", "platform": "chatomate", "type": "phone_number_quality_update", "subtype": "UNVERIFIED", "header": "<html><head><title>waba/template notifications</title></head><body style='font-family: Arial, Helvetica, sans-serif; background-color: #edf2f6; color: black; font-size: 16px; padding-top: 20px;'><div class='template' style='padding: 20px 0px 0px; border-radius: 10px; background-color: #ffffff; width: 690px; margin: 20px auto;'><img class='logo' src='{{logo}}' alt='Logo' style='width: auto; height: 75px; display: block; margin: 20px auto;' /><div class='mainText' style='margin: 30px; padding: 0px 30px;'><p><strong>Alert!</strong></p><p style='font-size: large;'>Dear {{userName}},</p>"}, {"subject": "Your WhatsApp Message Template Quality has been Changed", "html": "<p>We're reaching out to inform you about a recent update to your WhatsApp message template quality.</p><div class='campaignDetails' style='background-color: #2198fa25; border-radius: 5px; padding: 30px 20px; margin: 40px 15px;'><div style='color: #002f51; margin-bottom: 20px; font-weight: bold'>Template Details</div><div class='detailItem' style='margin: 3px 0px'><span class='detailKey' style='color: #2198fa; font-weight: bold'>Template Name</span><span class='detailValue' style='color: #002f51; font-weight: bold'>&nbsp;- {{templateName}}</span></div><div class='detailItem' style='margin: 3px 0px'><span class='detailKey' style='color: #2198fa; font-weight: bold'>Language</span><span class='detailValue' style='color: #002f51; font-weight: bold'>&nbsp;- {{language}}</span></div><div class='detailItem' style='margin: 3px 0px'><span class='detailKey' style='color: #2198fa; font-weight: bold'>Previous Quality</span><span class='detailValue' style='color: #002f51; font-weight: bold'>&nbsp;- {{previousQuality}}</span></div><div class='detailItem' style='margin: 3px 0px'><span class='detailKey' style='color: #2198fa; font-weight: bold'>New Quality</span><span class='detailValue' style='color: #002f51; font-weight: bold'>&nbsp;- {{newQuality}}</span></div></div><p>This change in template quality may affect how recipients perceive your messages. We recommend reviewing your messaging strategy to ensure it aligns with the updated template quality.</p><a href='https://app.chatomate.in/dashboard/template' target='_blank' class='signIn' style='display: contents'><button style='background-color: #002f51; color: #ffffff; padding: 10px 45px; border: none; border-radius: 5px; cursor: pointer; margin: 30px auto; display: block;'>See Template</button></a>", "platform": "chatomate", "type": "message_template_quality_update", "subtype": "", "header": "<html><head><title>waba/template notifications</title></head><body style='font-family: Arial, Helvetica, sans-serif; background-color: #edf2f6; color: black; font-size: 16px; padding-top: 20px;'><div class='template' style='padding: 20px 0px 0px; border-radius: 10px; background-color: #ffffff; width: 690px; margin: 20px auto;'><img class='logo' src='{{logo}}' alt='Logo' style='width: auto; height: 75px; display: block; margin: 20px auto;' /><div class='mainText' style='margin: 30px; padding: 0px 30px;'><p style='font-size: large;'>Dear {{userName}},</p>"}, {"subject": "Your WhatsApp Message Template Quality has been Changed", "html": "<p>We're here to inform you that your WhatsApp Message Template <span class='highlight' style='font-weight: bold;'>{{templateName}}</span> has been <span class='highlight' style='font-weight: bold;'>Approved</span>!</p><div class='campaignDetails' style='background-color: #2198fa25; border-radius: 5px; padding: 30px 20px; margin: 40px 15px;'><div style='color: #002f51; margin-bottom: 20px; font-weight: bold;'>Template Details</div><div class='detailItem' style='margin: 3px 0px;'><span class='detailKey' style='color: #2198fa; font-weight: bold;'>Template Name:</span><span class='detailValue' style='color: #002f51; font-weight: bold;'>&nbsp;{{templateName}}</span></div><div class='detailItem' style='margin: 3px 0px;'><span class='detailKey' style='color: #2198fa; font-weight: bold;'>Template Status:</span><span class='detailValue' style='color: #002f51; font-weight: bold;'>&nbsp;Approved</span></div><div class='detailItem' style='margin: 3px 0px;'><span class='detailKey' style='color: #2198fa; font-weight: bold;'>Language:</span><span class='detailValue' style='color: #002f51; font-weight: bold;'>&nbsp;{{language}}</span></div><div class='detailItem' style='margin: 3px 0px;'><span class='detailKey' style='color: #2198fa; font-weight: bold;'>Created By:</span><span class='detailValue' style='color: #002f51; font-weight: bold;'>&nbsp;{{createdBy}}</span></div><div class='detailItem' style='margin: 3px 0px;'><span class='detailKey' style='color: #2198fa; font-weight: bold;'>Created At:</span><span class='detailValue' style='color: #002f51; font-weight: bold;'>&nbsp;{{createdAt}}</span></div></div><a href='https://app.chatomate.in/dashboard/template' target='_blank' class='signIn' style='display: contents;'><button style='background-color: #002f51; color: #ffffff; padding: 10px 45px; border: none; border-radius: 5px; cursor: pointer; margin: 30px auto; display: block;'>See Template</button></a><p>Thank you for trusting our platform for your messaging needs. We're here to help you succeed every step of the way! 🌟</p>", "platform": "chatomate", "type": "message_template_status_update", "subtype": "APPROVED", "header": "<html><head><title>waba/template notifications</title></head><body style='font-family: Arial, Helvetica, sans-serif; background-color: #edf2f6; color: black; font-size: 16px; padding-top: 20px;'><div class='template' style='padding: 20px 0px 0px; border-radius: 10px; background-color: #ffffff; width: 690px; margin: 20px auto;'><img class='logo' src='{{logo}}' alt='Logo' style='width: auto; height: 75px; display: block; margin: 20px auto;' /><div class='mainText' style='margin: 30px; padding: 0px 30px;'><p style='font-size: large;'>Dear {{userName}},</p>"}, {"subject": "Your WhatsApp Message Template Quality has been Changed", "html": "<p>We're here to inform you that your WhatsApp Message Template <span class='highlight' style='font-weight: bold;'>{{templateName}}</span> has been <span class='highlight' style='font-weight: bold;'>InAppeal</span>!</p><div class='campaignDetails' style='background-color: #2198fa25; border-radius: 5px; padding: 30px 20px; margin: 40px 15px;'><div style='color: #002f51; margin-bottom: 20px; font-weight: bold;'>Template Details</div><div class='detailItem' style='margin: 3px 0px;'><span class='detailKey' style='color: #2198fa; font-weight: bold;'>Template Name:</span><span class='detailValue' style='color: #002f51; font-weight: bold;'>&nbsp;{{templateName}}</span></div><div class='detailItem' style='margin: 3px 0px;'><span class='detailKey' style='color: #2198fa; font-weight: bold;'>Template Status:</span><span class='detailValue' style='color: #002f51; font-weight: bold;'>&nbsp;InAppeal</span></div><div class='detailItem' style='margin: 3px 0px;'><span class='detailKey' style='color: #2198fa; font-weight: bold;'>Language:</span><span class='detailValue' style='color: #002f51; font-weight: bold;'>&nbsp;{{language}}</span></div><div class='detailItem' style='margin: 3px 0px;'><span class='detailKey' style='color: #2198fa; font-weight: bold;'>Created By:</span><span class='detailValue' style='color: #002f51; font-weight: bold;'>&nbsp;{{createdBy}}</span></div><div class='detailItem' style='margin: 3px 0px;'><span class='detailKey' style='color: #2198fa; font-weight: bold;'>Created At:</span><span class='detailValue' style='color: #002f51; font-weight: bold;'>&nbsp;{{createdAt}}</span></div></div><a href='https://app.chatomate.in/dashboard/template' target='_blank' class='signIn' style='display: contents;'><button style='background-color: #002f51; color: #ffffff; padding: 10px 45px; border: none; border-radius: 5px; cursor: pointer; margin: 30px auto; display: block;'>See Template</button></a><p>Thank you for trusting our platform for your messaging needs. We're here to help you succeed every step of the way! 🌟</p>", "platform": "chatomate", "type": "message_template_status_update", "subtype": "IN_APPEAL", "header": "<html><head><title>waba/template notifications</title></head><body style='font-family: Arial, Helvetica, sans-serif; background-color: #edf2f6; color: black; font-size: 16px; padding-top: 20px;'><div class='template' style='padding: 20px 0px 0px; border-radius: 10px; background-color: #ffffff; width: 690px; margin: 20px auto;'><img class='logo' src='{{logo}}' alt='Logo' style='width: auto; height: 75px; display: block; margin: 20px auto;' /><div class='mainText' style='margin: 30px; padding: 0px 30px;'><p style='font-size: large;'>Dear {{userName}},</p>"}, {"subject": "Your WhatsApp Message Template Quality has been Changed", "html": "<p>We're here to inform you that your WhatsApp Message Template <span class='highlight' style='font-weight: bold;'>{{templateName}}</span> has been <span class='highlight' style='font-weight: bold;'>Pending</span>!</p><div class='campaignDetails' style='background-color: #2198fa25; border-radius: 5px; padding: 30px 20px; margin: 40px 15px;'><div style='color: #002f51; margin-bottom: 20px; font-weight: bold;'>Template Details</div><div class='detailItem' style='margin: 3px 0px;'><span class='detailKey' style='color: #2198fa; font-weight: bold;'>Template Name:</span><span class='detailValue' style='color: #002f51; font-weight: bold;'>&nbsp;{{templateName}}</span></div><div class='detailItem' style='margin: 3px 0px;'><span class='detailKey' style='color: #2198fa; font-weight: bold;'>Template Status:</span><span class='detailValue' style='color: #002f51; font-weight: bold;'>&nbsp;Pending</span></div><div class='detailItem' style='margin: 3px 0px;'><span class='detailKey' style='color: #2198fa; font-weight: bold;'>Language:</span><span class='detailValue' style='color: #002f51; font-weight: bold;'>&nbsp;{{language}}</span></div><div class='detailItem' style='margin: 3px 0px;'><span class='detailKey' style='color: #2198fa; font-weight: bold;'>Created By:</span><span class='detailValue' style='color: #002f51; font-weight: bold;'>&nbsp;{{createdBy}}</span></div><div class='detailItem' style='margin: 3px 0px;'><span class='detailKey' style='color: #2198fa; font-weight: bold;'>Created At:</span><span class='detailValue' style='color: #002f51; font-weight: bold;'>&nbsp;{{createdAt}}</span></div></div><a href='https://app.chatomate.in/dashboard/template' target='_blank' class='signIn' style='display: contents;'><button style='background-color: #002f51; color: #ffffff; padding: 10px 45px; border: none; border-radius: 5px; cursor: pointer; margin: 30px auto; display: block;'>See Template</button></a><p>Thank you for trusting our platform for your messaging needs. We're here to help you succeed every step of the way! 🌟</p>", "platform": "chatomate", "type": "message_template_status_update", "subtype": "PENDING", "header": "<html><head><title>waba/template notifications</title></head><body style='font-family: Arial, Helvetica, sans-serif; background-color: #edf2f6; color: black; font-size: 16px; padding-top: 20px;'><div class='template' style='padding: 20px 0px 0px; border-radius: 10px; background-color: #ffffff; width: 690px; margin: 20px auto;'><img class='logo' src='{{logo}}' alt='Logo' style='width: auto; height: 75px; display: block; margin: 20px auto;' /><div class='mainText' style='margin: 30px; padding: 0px 30px;'><p style='font-size: large;'>Dear {{userName}},</p>"}, {"subject": "Your WhatsApp Message Template Quality has been Changed", "html": "<p>We're here to inform you that your WhatsApp Message Template <span class='highlight' style='font-weight: bold;'>{{templateName}}</span> has been <span class='highlight' style='font-weight: bold;'>Reinstated</span>!</p><div class='campaignDetails' style='background-color: #2198fa25; border-radius: 5px; padding: 30px 20px; margin: 40px 15px;'><div style='color: #002f51; margin-bottom: 20px; font-weight: bold;'>Template Details</div><div class='detailItem' style='margin: 3px 0px;'><span class='detailKey' style='color: #2198fa; font-weight: bold;'>Template Name:</span><span class='detailValue' style='color: #002f51; font-weight: bold;'>&nbsp;{{templateName}}</span></div><div class='detailItem' style='margin: 3px 0px;'><span class='detailKey' style='color: #2198fa; font-weight: bold;'>Template Status:</span><span class='detailValue' style='color: #002f51; font-weight: bold;'>&nbsp;Reinstated</span></div><div class='detailItem' style='margin: 3px 0px;'><span class='detailKey' style='color: #2198fa; font-weight: bold;'>Language:</span><span class='detailValue' style='color: #002f51; font-weight: bold;'>&nbsp;{{language}}</span></div><div class='detailItem' style='margin: 3px 0px;'><span class='detailKey' style='color: #2198fa; font-weight: bold;'>Created By:</span><span class='detailValue' style='color: #002f51; font-weight: bold;'>&nbsp;{{createdBy}}</span></div><div class='detailItem' style='margin: 3px 0px;'><span class='detailKey' style='color: #2198fa; font-weight: bold;'>Created At:</span><span class='detailValue' style='color: #002f51; font-weight: bold;'>&nbsp;{{createdAt}}</span></div></div><a href='https://app.chatomate.in/dashboard/template' target='_blank' class='signIn' style='display: contents;'><button style='background-color: #002f51; color: #ffffff; padding: 10px 45px; border: none; border-radius: 5px; cursor: pointer; margin: 30px auto; display: block;'>See Template</button></a><p>Thank you for trusting our platform for your messaging needs. We're here to help you succeed every step of the way! 🌟</p>", "platform": "chatomate", "type": "message_template_status_update", "subtype": "REINSTATED", "header": "<html><head><title>waba/template notifications</title></head><body style='font-family: Arial, Helvetica, sans-serif; background-color: #edf2f6; color: black; font-size: 16px; padding-top: 20px;'><div class='template' style='padding: 20px 0px 0px; border-radius: 10px; background-color: #ffffff; width: 690px; margin: 20px auto;'><img class='logo' src='{{logo}}' alt='Logo' style='width: auto; height: 75px; display: block; margin: 20px auto;' /><div class='mainText' style='margin: 30px; padding: 0px 30px;'><p style='font-size: large;'>Dear {{userName}},</p>"}, {"subject": "Your WhatsApp message template has been rejected", "html": "<p>We regret to inform you that your WhatsApp Message Template <span class='highlight'>{{templateName}}</span> has been rejected. However, you have the opportunity to make the necessary changes and resubmit it for approval.</p><div class='campaignDetails' style='background-color: #2198fa25; border-radius: 5px; padding: 30px 20px; margin: 40px 15px;'><div style='color: #002f51; margin-bottom: 20px; font-weight: bold'>Template Details</div><div class='detailItem' style='margin: 3px 0px'><span class='detailKey' style='color: #2198fa; font-weight: bold'>Template Name:</span><span class='detailValue' style='color: #002f51; font-weight: bold'>&nbsp;{{templateName}}</span></div><div class='detailItem' style='margin: 3px 0px'><span class='detailKey' style='color: #2198fa; font-weight: bold'>Template Status:</span><span class='detailValue' style='color: #002f51; font-weight: bold'>&nbsp;Rejected</span></div><div class='detailItem' style='margin: 3px 0px'><span class='detailKey' style='color: #2198fa; font-weight: bold'>Language:</span><span class='detailValue' style='color: #002f51; font-weight: bold'>&nbsp;{{language}}</span></div><div class='detailItem' style='margin: 3px 0px'><span class='detailKey' style='color: #2198fa; font-weight: bold'>Created By:</span><span class='detailValue' style='color: #002f51; font-weight: bold'>&nbsp;{{createdBy}}</span></div><div class='detailItem' style='margin: 3px 0px'><span class='detailKey' style='color: #2198fa; font-weight: bold'>Created At:</span><span class='detailValue' style='color: #002f51; font-weight: bold'>&nbsp;{{createdAt}}</span></div><div class='detailItem' style='margin: 3px 0px'><span class='detailKey' style='color: #2198fa; font-weight: bold'>Rejection Reason:</span><span class='detailValue' style='color: #002f51; font-weight: bold'>&nbsp;{{rejectionReason}}</span></div></div><a href='https://app.chatomate.in/dashboard/template' target='_blank' class='signIn' style='display: contents'><button style='background-color: #002f51; color: #ffffff; padding: 10px 45px; border: none; border-radius: 5px; cursor: pointer; margin: 30px auto; display: block;'>See Template</button></a><p>Thank you for trusting our platform for your messaging needs. We are here to help you succeed every step of the way! 🌟</p>", "platform": "chatomate", "type": "message_template_status_update", "subtype": "REJECTED", "header": "<html><head><title>waba/template notifications</title></head><body style='font-family: Arial, Helvetica, sans-serif; background-color: #edf2f6; color: black; font-size: 16px; padding-top: 20px;'><div class='template' style='padding: 20px 0px 0px; border-radius: 10px; background-color: #ffffff; width: 690px; margin: 20px auto;'><img class='logo' src='{{logo}}' alt='Logo' style='width: auto; height: 75px; display: block; margin: 20px auto;' /><div class='mainText' style='margin: 30px; padding: 0px 30px;'><p style='font-size: large;'>Dear {{userName}},</p>"}, {"subject": "Your WhatsApp message template has been flagged", "html": "<p>We regret to inform you that your WhatsApp Message Template <span class='highlight'>{{templateName}}</span> has been Flagged.</p><div class='campaignDetails' style='background-color: #2198fa25; border-radius: 5px; padding: 30px 20px; margin: 40px 15px;'><div style='color: #002f51; margin-bottom: 20px; font-weight: bold'>Template Details:</div><div class='detailItem' style='margin: 3px 0px'><span class='detailKey' style='color: #2198fa; font-weight: bold'>Template Name:</span><span class='detailValue' style='color: #002f51; font-weight: bold'>&nbsp;{{templateName}}</span></div><div class='detailItem' style='margin: 3px 0px'><span class='detailKey' style='color: #2198fa; font-weight: bold'>Template Status:</span><span class='detailValue' style='color: #002f51; font-weight: bold'>&nbsp;Flagged</span></div><div class='detailItem' style='margin: 3px 0px'><span class='detailKey' style='color: #2198fa; font-weight: bold'>Language:</span><span class='detailValue' style='color: #002f51; font-weight: bold'>&nbsp;{{language}}</span></div><div class='detailItem' style='margin: 3px 0px'><span class='detailKey' style='color: #2198fa; font-weight: bold'>Created By:</span><span class='detailValue' style='color: #002f51; font-weight: bold'>&nbsp;{{createdBy}}</span></div><div class='detailItem' style='margin: 3px 0px'><span class='detailKey' style='color: #2198fa; font-weight: bold'>Created At:</span><span class='detailValue' style='color: #002f51; font-weight: bold'>&nbsp;{{createdAt}}</span></div></div><a href='https://app.chatomate.in/dashboard/template' target='_blank' class='signIn' style='display: contents'><button style='background-color: #002f51; color: #ffffff; padding: 10px 45px; border: none; border-radius: 5px; cursor: pointer; margin: 30px auto; display: block;'>See Template</button></a>", "platform": "chatomate", "type": "message_template_status_update", "subtype": "FLAGGED", "header": "<html><head><title>waba/template notifications</title></head><body style='font-family: Arial, Helvetica, sans-serif; background-color: #edf2f6; color: black; font-size: 16px; padding-top: 20px;'><div class='template' style='padding: 20px 0px 0px; border-radius: 10px; background-color: #ffffff; width: 690px; margin: 20px auto;'><img class='logo' src='{{logo}}' alt='Logo' style='width: auto; height: 75px; display: block; margin: 20px auto;' /><div class='mainText' style='margin: 30px; padding: 0px 30px;'><p style='font-size: large;'>Dear {{userName}},</p>"}, {"subject": "Your WhatsApp message template has been disabled", "html": "<p>We regret to inform you that your WhatsApp Message Template <span class='highlight' style='font-weight: bold;'>{{templateName}}</span> was disabled on <span class='highlight' style='font-weight: bold;'>{{time}}</span> due to continued issues. For instance, your customers have been blocking or reporting your phone number after receiving the message.</p> <div class='campaignDetails' style='background-color: #2198fa25; border-radius: 5px; padding: 30px 20px; margin: 40px 15px;'> <div style='color: #002f51; margin-bottom: 20px; font-weight: bold'>Template Details</div> <div class='detailItem' style='margin: 3px 0px'> <span class='detailKey' style='color: #2198fa; font-weight: bold'>Template Name:</span> <span class='detailValue' style='color: #002f51; font-weight: bold'>&nbsp; {{templateName}}</span> </div> <div class='detailItem' style='margin: 3px 0px'> <span class='detailKey' style='color: #2198fa; font-weight: bold'>Template Status:</span> <span class='detailValue' style='color: #002f51; font-weight: bold'>&nbsp; Disabled</span> </div> <div class='detailItem' style='margin: 3px 0px'> <span class='detailKey' style='color: #2198fa; font-weight: bold'>Language:</span> <span class='detailValue' style='color: #002f51; font-weight: bold'>&nbsp; {{language}}</span> </div> <div class='detailItem' style='margin: 3px 0px'> <span class='detailKey' style='color: #2198fa; font-weight: bold'>Created By:</span> <span class='detailValue' style='color: #002f51; font-weight: bold'>&nbsp; {{createdBy}}</span> </div> <div class='detailItem' style='margin: 3px 0px'> <span class='detailKey' style='color: #2198fa; font-weight: bold'>Created At:</span> <span class='detailValue' style='color: #002f51; font-weight: bold'>&nbsp; {{createAt}}</span> </div> </div> <a href='https://app.chatomate.in/dashboard/template' target='_blank' class='signIn' style='display: contents'> <button style='background-color: #002f51; color: #ffffff; padding: 10px 45px; border: none; border-radius: 5px; cursor: pointer; margin: 30px auto; display: block;'>See Template</button> </a>", "platform": "chatomate", "type": "message_template_status_update", "subtype": "DISABLED", "header": "<html><head><title>waba/template notifications</title></head><body style='font-family: Arial, Helvetica, sans-serif; background-color: #edf2f6; color: black; font-size: 16px; padding-top: 20px;'><div class='template' style='padding: 20px 0px 0px; border-radius: 10px; background-color: #ffffff; width: 690px; margin: 20px auto;'><img class='logo' src='{{logo}}' alt='Logo' style='width: auto; height: 75px; display: block; margin: 20px auto;' /><div class='mainText' style='margin: 30px; padding: 0px 30px;'><p style='font-size: large;'>Dear {{userName}},</p>"}, {"subject": "Your WhatsApp message template category has been changed", "html": "<p>We're here to inform you about the category change of your template <span class='highlight'>{{templateName}}</span>!</p><div class='campaignDetails' style='background-color: #2198fa25; border-radius: 5px; padding: 30px 20px; margin: 40px 15px;'><div style='color: #002f51; margin-bottom: 20px; font-weight: bold'>Template Details:</div><div class='detailItem' style='margin: 3px 0px'><span class='detailKey' style='color: #2198fa; font-weight: bold'>Template Name:</span><span class='detailValue' style='color: #002f51; font-weight: bold'>&nbsp;{{templateName}}</span></div><div class='detailItem' style='margin: 3px 0px'><span class='detailKey' style='color: #2198fa; font-weight: bold'>Language:</span><span class='detailValue' style='color: #002f51; font-weight: bold'>&nbsp;{{language}}</span></div><div class='detailItem' style='margin: 3px 0px'><span class='detailKey' style='color: #2198fa; font-weight: bold'>Previous Category:</span><span class='detailValue' style='color: #002f51; font-weight: bold'>&nbsp;{{previousCategory}}</span></div><div class='detailItem' style='margin: 3px 0px'><span class='detailKey' style='color: #2198fa; font-weight: bold'>New Category:</span><span class='detailValue' style='color: #002f51; font-weight: bold'>&nbsp;{{newCategory}}</span></div></div><a href='https://app.chatomate.in/dashboard/template' target='_blank' class='signIn' style='display: contents'><button style='background-color: #002f51; color: #ffffff; padding: 10px 45px; border: none; border-radius: 5px; cursor: pointer; margin: 30px auto; display: block;'>See Template</button></a><p>This update may impact how your messages are categorized and delivered to recipients. We recommend reviewing your messaging strategy to ensure it aligns with the new template category.</p>", "platform": "chatomate", "type": "template_category_update", "subtype": "", "header": "<html><head><title>waba/template notifications</title></head><body style='font-family: Arial, Helvetica, sans-serif; background-color: #edf2f6; color: black; font-size: 16px; padding-top: 20px;'><div class='template' style='padding: 20px 0px 0px; border-radius: 10px; background-color: #ffffff; width: 690px; margin: 20px auto;'><img class='logo' src='{{logo}}' alt='Logo' style='width: auto; height: 75px; display: block; margin: 20px auto;' /><div class='mainText' style='margin: 30px; padding: 0px 30px;'><p style='font-size: large;'>Dear {{userName}},</p>"}]