<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Your WhatsApp Message Template Category Changed</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        color: #333;
        line-height: 1.6;
        background-color: #f4f4f4;
      }
      .container {
        max-width: 600px;
        margin: 20px auto;
        padding: 20px;
        border: 1px solid #ddd;
        border-radius: 10px;
        background-color: #fff;
      }
      .highlight {
        font-weight: bold;
      }
      .details {
        margin-top: 20px;
      }
      .footer {
        margin-top: 20px;
        font-size: 0.9em;
        color: #555;
      }
      a {
        color: #0056b3;
        text-decoration: none;
      }
      .button {
        text-align: center;
        margin-top: 20px;
      }
      .button a {
        display: inline-block;
        padding: 10px 20px;
        background-color: #28a745;
        color: white;
        text-decoration: none;
        border-radius: 5px;
        font-weight: bold;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <p><strong>Dear [User Name],</strong></p>
      <p>
        We're here to inform you about the category change of your template
        <span class="highlight">{{templateName}}</span>!
      </p>
      <div
      class="campaignDetails"
      style="
        background-color: #2198fa25;
        border-radius: 5px;
        padding: 30px 20px;
        margin: 40px 15px;
      "
    >
      <div style="color: #002f51; margin-bottom: 20px; font-weight: bold">
        Template Details:
      </div>
      <div class="detailItem" style="margin: 3px 0px">
        <span class="detailKey" style="color: #2198fa; font-weight: bold"
          >Template Name:</span
        >
        <span class="detailValue" style="color: #002f51; font-weight: bold"
          >&nbsp;{{templateName}}</span
        >
      </div>
      <div class="detailItem" style="margin: 3px 0px">
        <span class="detailKey" style="color: #2198fa; font-weight: bold"
          >Language:</span
        >
        <span class="detailValue" style="color: #002f51; font-weight: bold"
          >&nbsp;{{language}}</span
        >
      </div>
      <div class="detailItem" style="margin: 3px 0px">
        <span class="detailKey" style="color: #2198fa; font-weight: bold"
          >Previous Category:</span
        >
        <span class="detailValue" style="color: #002f51; font-weight: bold"
          >&nbsp;{{previousCategory}}</span
        >
      </div>
      <div class="detailItem" style="margin: 3px 0px">
        <span class="detailKey" style="color: #2198fa; font-weight: bold"
          >New Category:</span
        >
        <span class="detailValue" style="color: #002f51; font-weight: bold"
          >&nbsp;{{newCategory}}</span
        >
      </div>
    </div>

    <a
      href="https://app.chatomate.in/dashboard/template"
      target="_blank"
      class="signIn"
      style="display: contents"
    >
      <button
        style="
          background-color: #002f51;
          color: #ffffff;
          padding: 10px 45px;
          border: none;
          border-radius: 5px;
          cursor: pointer;
          margin: 30px auto;
          display: block;
        "
      >
        See Template
      </button>
    </a>

      <p>
        This update may impact how your messages are categorized and delivered
        to recipients. We recommend reviewing your messaging strategy to ensure
        it aligns with the new template category.
      </p>

      <div class="footer">
        For any questions or assistance, our support team is ready to provide
        the help you need.
        <br />
        Call: <a href="tel:+919726320541">(+91) 97263 20541</a> Email:
        <a href="mailto:<EMAIL>"><EMAIL></a>
      </div>
    </div>
  </body>
</html>
