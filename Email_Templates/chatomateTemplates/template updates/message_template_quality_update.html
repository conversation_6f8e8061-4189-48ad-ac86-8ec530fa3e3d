<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>WhatsApp Message Template Quality Changed</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        color: #333;
        line-height: 1.6;
        background-color: #f4f4f4;
      }
      .container {
        max-width: 600px;
        margin: 20px auto;
        padding: 20px;
        border: 1px solid #ddd;
        border-radius: 10px;
        background-color: #fff;
      }
      .highlight {
        font-weight: bold;
      }
      .footer {
        margin-top: 20px;
        font-size: 0.9em;
        color: #555;
      }
      a {
        color: #0056b3;
        text-decoration: none;
      }
      .button {
        text-align: center;
        margin-top: 20px;
      }
      .button a {
        display: inline-block;
        padding: 10px 20px;
        background-color: #28a745;
        color: white;
        text-decoration: none;
        border-radius: 5px;
        font-weight: bold;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <p>Dear [User],</p>
      <p>
        We're reaching out to inform you about a recent update to your WhatsApp
        message template quality.
      </p>
      <div
        class="campaignDetails"
        style="
          background-color: #2198fa25;
          border-radius: 5px;
          padding: 30px 20px;
          margin: 40px 15px;
        "
      >
        <div style="color: #002f51; margin-bottom: 20px; font-weight: bold">
          Template Details
        </div>
        <div class="detailItem" style="margin: 3px 0px">
          <span class="detailKey" style="color: #2198fa; font-weight: bold"
            >Template Name</span
          >
          <span class="detailValue" style="color: #002f51; font-weight: bold"
            >&nbsp;- {{templateName}}</span
          >
        </div>
        <div class="detailItem" style="margin: 3px 0px">
          <span class="detailKey" style="color: #2198fa; font-weight: bold"
            >Language</span
          >
          <span class="detailValue" style="color: #002f51; font-weight: bold"
            >&nbsp;- {{language}}</span
          >
        </div>
        <div class="detailItem" style="margin: 3px 0px">
          <span class="detailKey" style="color: #2198fa; font-weight: bold"
            >Previous Quality</span
          >
          <span class="detailValue" style="color: #002f51; font-weight: bold"
            >&nbsp;- {{previousQuality}}</span
          >
        </div>
        <div class="detailItem" style="margin: 3px 0px">
          <span class="detailKey" style="color: #2198fa; font-weight: bold"
            >New Quality</span
          >
          <span class="detailValue" style="color: #002f51; font-weight: bold"
            >&nbsp;- {{newQuality}}</span
          >
        </div>
      </div>

      <p>
        This change in template quality may affect how recipients perceive your
        messages. We recommend reviewing your messaging strategy to ensure it
        aligns with the updated template quality.
      </p>

      <a
        href="https://app.chatomate.in/dashboard/template"
        target="_blank"
        class="signIn"
        style="display: contents"
      >
        <button
          style="
            background-color: #002f51;
            color: #ffffff;
            padding: 10px 45px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 30px auto;
            display: block;
          "
        >
          See Template
        </button>
      </a>

      Best Regards,<br />
      Chatomate
      <div class="footer">
        For any questions or assistance, our support team is ready to provide
        the help you need.
        <br />
        Call: <a href="tel:+919726320541">(+91) 97263 20541</a> Email:
        <a href="mailto:<EMAIL>"><EMAIL></a>
      </div>
    </div>
  </body>
</html>
