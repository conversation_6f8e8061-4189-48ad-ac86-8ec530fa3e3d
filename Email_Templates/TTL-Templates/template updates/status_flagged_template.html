<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Your WhatsApp Message Template Flagged</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        color: #333;
        line-height: 1.6;
        background-color: #f4f4f4;
      }
      .container {
        max-width: 600px;
        margin: 20px auto;
        padding: 20px;
        border: 1px solid #ddd;
        border-radius: 10px;
        background-color: #fff;
      }
      .highlight {
        font-weight: bold;
      }
      .details {
        margin-top: 20px;
      }
      .footer {
        margin-top: 20px;
        font-size: 0.9em;
        color: #555;
      }
      a {
        color: #0056b3;
        text-decoration: none;
      }
      .button {
        text-align: center;
        margin-top: 20px;
      }
      .button a {
        display: inline-block;
        padding: 10px 20px;
        background-color: #28a745;
        color: white;
        text-decoration: none;
        border-radius: 5px;
        font-weight: bold;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <p><strong>Dear [User Name],</strong></p>
      <p>Greetings from Tata Tele Business Services.</p>

      <p>
        We regret to inform you that your WhatsApp Message Template
        <span class="highlight" style="font-weight: bold"
          >{{templateName}}</span
        >
        has been <span style="font-weight: bold">flagged</span>.
      </p>
      <div class="details">
        <p><strong>Template Details:</strong></p>
        <ul>
          <li><strong>Template Name:</strong> {{templateName}}</li>
          <li><strong>Template Status:</strong> Flagged</li>
          <li><strong>Language:</strong> {{language}}</li>
          <li><strong>Created By:</strong> {{createdBy}}</li>
          <li><strong>Created At:</strong> {{createdAt}}</li>
        </ul>
      </div>

      <div style="text-align: center">
        <a
          href="{{appPanelUrl}}"
          style="
            background-color: {{secondaryColor}};
            color: white;
            width: 200px;
            padding: 8px 30px;
            font-size: 16px;
            border-radius: 5px;
            text-decoration: none;
          "
          >See Template</a
        >
      </div>

      <div class="footer">
        Thank you for making Smartflo your go-to platform for your communication
        requirements.
        <br />
        Assuring you of our Best Services, always.
      </div>
      <p>
        Best Regards,<br />
        Smartflo Omni Channel
      </p>
    </div>
  </body>
</html>

\

{
  "_id": {
    "$oid": "633fbff8d76dbf4119567f21"
  },
  "accessingEntityId": "633fbff8d76dbf4119567f1f",
  "accessingEntityType": "user",
  "orgAccess": [
    {
      "organizationId": {
        "$oid": "63412e33d76dbf411956d2c2"
      },
      "isOrgIdNull": false,
      "isOrgShared": false,
      "permissions": [],
      "botAccess": [
        {
          "botId": {
            "$oid": "63414740d76dbf411956d32b"
          },
          "permissions": [
            "bot.get",
            "bot.update"
          ],
          "role": "Bot Developer",
          "_id": {
            "$oid": "6346d8c8827655708ae58b4c"
          }
        }
      ],
      "_id": {
        "$oid": "6346d8c8827655708ae58b4b"
      }
    },
    {
      "organizationId": {
        "$oid": "627cd72b9270eec72fa82a3f"
      },
      "isOrgIdNull": false,
      "isOrgShared": false,
      "permissions": [],
      "botAccess": [
        {
          "botId": {
            "$oid": "627cd99d9d55aa3fd499b5b9"
          },
          "permissions": [
            "bot.get",
            "bot.update"
          ],
          "role": "Organization Admin",
          "_id": {
            "$oid": "6704c5c9506620209f9e05f9"
          }
        },
        {
          "botId": {
            "$oid": "627cd99d9d55aa3fd499b5b8"
          },
          "permissions": [
            "bot.get",
            "bot.update"
          ],
          "role": "Bot Developer",
          "_id": {
            "$oid": "6704c5c9506620209f9e05f8"
          }
        }
      ],
      "_id": {
        "$oid": "6704c5c9506620209f9e05f8"
      }
    }
  ],
  "createdAt": {
    "$date": "2022-10-07T05:58:16.233Z"
  },
  "updatedAt": {
    "$date": "2024-10-08T05:40:25.983Z"
  },
  "__v": 7
}