<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>WhatsApp Phone Number Status Changed</title>
    <!-- <style>
      /* body {
        font-family: Arial, sans-serif;
        color: #333;
        line-height: 1.6;
        background-color: #f4f4f4;
      }
      .container {
        max-width: 600px;
        margin: 20px auto;
        padding: 20px;
        border: 1px solid #ddd;
        border-radius: 10px;
        background-color: #fff;
      }
      .highlight {
        font-weight: bold;
      }
      .footer {
        margin-top: 20px;
        font-size: 0.9em;
        color: #555;
      } */
    </style> -->
  </head>
  <body>
    <p>
      We regret to inform you that your recent campaign,${campaignName}, has
      encountered an unexpected issue and didn't go as planned. We apologize for
      any inconvenience this may have caused.
    </p>
    <div
      class="campaignDetails"
      style="
        background-color: #f56d2325;
        border-radius: 5px;
        padding: 30px 20px;
        margin: 40px 15px;
      "
    >
      <div style="color: #002f51; margin-bottom: 20px; font-weight: bold">
        Campaign Details
      </div>
      <div class="detailItem" style="margin: 3px 0px">
        <span class="detailKey" style="color: #f56d23; font-weight: bold"
          >Campaign Name</span
        >
        <span class="detailValue" style="color: #002f51; font-weight: bold"
          >&nbsp;- ${campaignName}</span
        >
      </div>
      <div class="detailItem" style="margin: 3px 0px">
        <span class="detailKey" style="color: #f56d23; font-weight: bold"
          >Scheduled On</span
        >
        <span class="detailValue" style="color: #002f51; font-weight: bold"
          >&nbsp;- ${launchTime}</span
        >
      </div>
      <div class="detailItem" style="margin: 3px 0px">
        <span class="detailKey" style="color: #f56d23; font-weight: bold"
          >Reason for Failure</span
        >
        <span class="detailValue" style="color: #002f51; font-weight: bold"
          >&nbsp;- ${failedReason}</span
        >
      </div>
    </div>
    <div>
      <p>
        This notification is to ensure transparency and keep you promptly
        informed about the failure of your campaign.
      </p>
      <h4>Next Steps</h4>
      <ul>
        <li>
          We suggest giving the details a once-over to figure out why things
          didn't quite go according to plan.
        </li>
        <li>
          If you need assistance or want to reschedule, please reach out to our
          support team.
        </li>
      </ul>
      ${getSignInButton(secondaryColor, appPanelUrl)}
      <p>
        We're sorry for any inconvenience this may have caused and appreciate
        your understanding.
      </p>
    </div>
    <!-- <div class="container">
      <p><strong>Good News!</strong></p>
      <p>Dear [User],</p>
      <p>Greetings from Tata Tele Business Services.</p>
      <p>
        We are pleased to inform you that the status of the phone number
        <span class="highlight">[Phone Number]</span> associated with your
        WhatsApp Business account <span class="highlight">[WABA Name]</span> has
        been changed to <span class="highlight">Connected</span>.
      </p>
      <p>
        This update means your phone number is now fully operational and ready
        for use with your WhatsApp Business account. You can resume all
        messaging activities and engage with your customers without
        interruption.
      </p>
      <p>
        Thank you for making Smartflo your go-to platform for your communication
        requirements.
      </p>

      <p>Assuring you of our Best Services, always.</p>
      <p><strong>Best Regards,</strong> <br />Smartflo Omni Channel</p>
    </div> -->
  </body>
</html>
"<p>Thank you for making Smartflo your go-to platform for your communication requirements.</p><p>Assuring you of our best Services, always.</p>Best Regards,<br>Smartflo Omni Channel</div>"





